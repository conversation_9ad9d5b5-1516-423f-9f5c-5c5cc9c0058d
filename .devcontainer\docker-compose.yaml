version: '3'
services:
  metagpt:
    build:
      dockerfile: Dockerfile
      context: ..
    volumes:
   # Update this to wherever you want VS Code to mount the folder of your project
      - ..:/workspaces:cached
    networks:
      - metagpt-network 
  #   environment:
  #     MONGO_ROOT_USERNAME: root
  #     MONGO_ROOT_PASSWORD: example123
  #   depends_on:
  #     - mongo   
  # mongo:
  #   image: mongo
  #   restart: unless-stopped
  #   environment:
  #     MONGO_INITDB_ROOT_USERNAME: root
  #     MONGO_INITDB_ROOT_PASSWORD: example123
  #   ports:
  #     - "27017:27017"
  #   networks:
  #     - metagpt-network

networks:
  metagpt-network:
    driver: bridge
    
