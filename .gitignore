### Python template

# Byte-compiled / optimized / DLL files
__pycache__
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
metagpt/tools/schemas/
examples/data/search_kb/*.json

# PyInstaller
#  Usually these files are written by a python scripts from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/
unittest.txt

# Translations
*.mo
*.pot

# Django stuff:
*.log
logs
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# report
allure-report
allure-results

# idea / vscode / macos
.idea
.DS_Store
.vscode

key.yaml
/data/
data.ms
examples/nb/
examples/default__vector_store.json
examples/docstore.json
examples/graph_store.json
examples/image__vector_store.json
examples/index_store.json
.chroma
.chroma_exp_data
.role_memory_data
*~$*
workspace/*
tmp
metagpt/roles/idea_agent.py
.aider*
*.bak
*.bk

# output folder
output
tmp.png
.dependencies.json
tests/metagpt/utils/file_repo_git
tests/data/rsp_cache_new.json
tests/data/serdeser_storage/
*.tmp
*.png
htmlcov
htmlcov.*
cov.xml
*.dot
*.pkl
*.faiss
*-structure.csv
*-structure.json
*.dot
.python-version
tests/data/requirements/*.jpg
*.csv
metagpt/ext/sela/results/*
.chainlit/

metagpt/ext/aflow/data
metagpt/ext/aflow/scripts/optimized
