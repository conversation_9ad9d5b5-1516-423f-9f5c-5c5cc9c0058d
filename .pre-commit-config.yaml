default_stages: [ commit ]

# Install
# 1. pip install metagpt[dev]
# 2. pre-commit install
# 3. pre-commit run --all-files  # make sure all files are clean
repos:
  - repo: https://github.com/pycqa/isort
    rev: 5.11.5
    hooks:
      - id: isort
        args: ['--profile', 'black']
        exclude: >-
            (?x)^(
            .*__init__\.py$
            )

  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.0.284
    hooks:
      - id: ruff
        args: [ --fix ]

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        args: ['--line-length', '120']
