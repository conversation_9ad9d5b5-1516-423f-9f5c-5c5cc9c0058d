<svg aria-roledescription="classDiagram" role="graphics-document document" viewBox="-35 0 2644.728515625 846" style="max-width: 2644.73px; background-color: white;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:2px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg g.classGroup text{fill:#9370DB;fill:#131300;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#my-svg g.classGroup text .title{font-weight:bolder;}#my-svg .nodeLabel,#my-svg .edgeLabel{color:#131300;}#my-svg .edgeLabel .label rect{fill:#ECECFF;}#my-svg .label text{fill:#131300;}#my-svg .edgeLabel .label span{background:#ECECFF;}#my-svg .classTitle{font-weight:bolder;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .divider{stroke:#9370DB;stroke-width:1;}#my-svg g.clickable{cursor:pointer;}#my-svg g.classGroup rect{fill:#ECECFF;stroke:#9370DB;}#my-svg g.classGroup line{stroke:#9370DB;stroke-width:1;}#my-svg .classLabel .box{stroke:none;stroke-width:0;fill:#ECECFF;opacity:0.5;}#my-svg .classLabel .label{fill:#9370DB;font-size:10px;}#my-svg .relation{stroke:#333333;stroke-width:1;fill:none;}#my-svg .dashed-line{stroke-dasharray:3;}#my-svg .dotted-line{stroke-dasharray:1 2;}#my-svg #compositionStart,#my-svg .composition{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #compositionEnd,#my-svg .composition{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #extensionStart,#my-svg .extension{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #extensionEnd,#my-svg .extension{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #aggregationStart,#my-svg .aggregation{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #aggregationEnd,#my-svg .aggregation{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #lollipopStart,#my-svg .lollipop{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #lollipopEnd,#my-svg .lollipop{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg .edgeTerminals{font-size:11px;}#my-svg .classTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker aggregation classDiagram" id="classDiagram-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker aggregation classDiagram" id="classDiagram-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker extension classDiagram" id="classDiagram-extensionStart"><path d="M 1,7 L18,13 V 1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker extension classDiagram" id="classDiagram-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker composition classDiagram" id="classDiagram-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker composition classDiagram" id="classDiagram-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker dependency classDiagram" id="classDiagram-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker dependency classDiagram" id="classDiagram-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker lollipop classDiagram" id="classDiagram-lollipopStart"><circle r="6" cy="7" cx="6" fill="white" stroke="black"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path style="fill:none" class="edge-pattern-solid relation" id="id1" d="M327.8243999677835,189.25L314.3998905981529,195.29166666666666C300.97538122852234,201.33333333333334,274.12636248926117,213.41666666666666,260.7018531196306,225.5C247.27734375,237.58333333333334,247.27734375,249.66666666666666,247.27734375,255.70833333333334L247.27734375,261.75"/><path style="fill:none" class="edge-pattern-solid relation" id="id2" d="M314.900390625,149.2522003010371L257.9169921875,161.96016691753093C200.93359375,174.66813353402472,86.966796875,200.08406676701236,29.9833984375,231.12536671683952C-27,262.1666666666667,-27,298.8333333333333,-27,335.5C-27,372.1666666666667,-27,408.8333333333333,11.3955078125,433.46968391830416C49.791015625,458.106034503275,126.58203125,470.71206900655005,164.9775390625,477.0150862581875L203.373046875,483.31810350982505"/><path style="fill:none" class="edge-pattern-solid relation" id="id3" d="M688.6704856636597,189.25L700.8943500322165,195.29166666666666C713.1182144007731,201.33333333333334,737.5659431378866,213.41666666666666,794.92815386061,229.49442289598178C852.2903645833334,245.5721791252969,942.5670572916666,265.6443582505938,987.7054036458334,275.68044781324227L1032.84375,285.7165373758907"/><path style="fill:none" class="edge-pattern-solid relation" id="id4" d="M718.486328125,130.11079956565987L842.5406901041666,146.0089996380499C966.5950520833334,161.90719971043993,1214.7037760416667,193.70359985521995,1406.4563802083333,223.80220067696655C1598.208984375,253.9008014987132,1733.60546875,282.3016029974264,1801.3037109375,296.502003746783L1869.001953125,310.7024044961396"/><path style="fill:none" class="edge-pattern-solid relation" id="id5" d="M247.27734375,409.25L247.27734375,415.2916666666667C247.27734375,421.3333333333333,247.27734375,433.4166666666667,263.2337038230241,443.625C279.1900638960481,453.8333333333333,311.10278404209623,462.1666666666667,327.0591441151203,466.3333333333333L343.01550418814435,470.5"/><path style="fill:none" class="edge-pattern-solid relation" id="id6" d="M1310.6272551546392,200.5L1303.3143792955327,204.66666666666666C1296.0015034364262,208.83333333333334,1281.375751718213,217.16666666666666,1273.5136334348642,227.375C1265.6515151515152,237.58333333333334,1264.5530303030303,249.66666666666666,1264.003787878788,255.70833333333334L1263.4545454545455,261.75"/><path style="fill:none" class="edge-pattern-solid relation" id="id7" d="M759.69921875,420.5L759.69921875,424.6666666666667C759.69921875,428.8333333333333,759.69921875,437.1666666666667,758.0465796821305,445.5C756.3939406142612,453.8333333333333,753.0886624785222,462.1666666666667,751.436023410653,466.3333333333333L749.7833843427835,470.5"/><path style="fill:none" class="edge-pattern-solid relation" id="id8" d="M2378.583984375,420.5L2378.583984375,424.6666666666667C2378.583984375,428.8333333333333,2378.583984375,437.1666666666667,2185.4602864583335,455.38047046721C1992.3365885416667,473.5942742677533,1606.0891927083333,501.6885485355067,1412.9654947916667,515.7356856693833L1219.841796875,529.78282280326"/><path style="fill:none" class="edge-pattern-solid relation" id="id9" d="M502.7365496134021,663L493.6945205111684,667.1666666666666C484.65249140893474,671.3333333333334,466.56843320446734,679.6666666666666,457.5264041022337,688C448.484375,696.3333333333334,448.484375,704.6666666666666,448.484375,708.8333333333334L448.484375,713"/><path style="fill:none" class="edge-pattern-solid relation" id="id10" d="M920.4782941365979,663L929.5203232388316,667.1666666666666C938.5623523410653,671.3333333333334,956.6464105455326,679.6666666666666,965.6884396477662,688C974.73046875,696.3333333333334,974.73046875,704.6666666666666,974.73046875,708.8333333333334L974.73046875,713"/><path style="fill:none" class="edge-pattern-solid relation" id="id11" d="M1219.841796875,656.2236707776684L1249.9248046875,661.519725648057C1280.0078125,666.8157805184456,1340.173828125,677.4078902592228,1370.2568359375,686.870611796278C1400.33984375,696.3333333333334,1400.33984375,704.6666666666666,1400.33984375,708.8333333333334L1400.33984375,713"/><path style="fill:none" class="edge-pattern-solid relation" id="id12" d="M2122.76171875,166.75L2122.76171875,176.54166666666666C2122.76171875,186.33333333333334,2122.76171875,205.91666666666666,2113.0068729285035,223.625C2103.2520271070075,241.33333333333334,2083.742335464015,257.1666666666667,2073.9874896425185,265.0833333333333L2064.2326438210225,273"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(305.7100469195023, 182.75343250200726)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(257.27734375, 239.25)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(294.5550068315089, 138.42097884196102)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(183.5340666917026, 460.68136154540014)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(697.7125726517392, 210.45119484604595)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1014.0165144024626, 262.2758878284861)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(733.9376342064445, 147.2136396917185)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1849.9540790751034, 287.42927573617516)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(232.27734375, 426.75)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(324.8731271848848, 446.56516631432436)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1287.9963613077757, 196.13048701890096)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1274.977319107305, 240.67990507745293)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(742.9416967479798, 436.0444344681618)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(764.1105774009227, 453.25128100690455)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(2359.611460033709, 427.94052574606286)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1233.383859441717, 538.4737656404774)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(480.56516235864876, 656.7008264669273)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(462.3479613292247, 697.864454013464)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(930.0942777964791, 683.9471443937568)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(979.5119491036148, 688.950411454912)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1234.4760277973699, 674.0306717972325)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1404.4908458417297, 689.4478605194171)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(2107.76171875, 184.25)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2082.2731819468268, 268.619356822312)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g class="nodes"><g transform="translate(516.693359375, 104.25)" id="classId-User-0" class="node default"><rect height="170" width="403.5859375" y="-85" x="-201.79296875" class="outer title-state"/><line y2="-54.5" y1="-54.5" x2="201.79296875" x1="-201.79296875" class="divider"/><line y2="51.5" y1="51.5" x2="201.79296875" x1="-201.79296875" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -16.8828125, -77.5)" height="18.5" width="33.765625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">User</span></div></foreignObject><foreignObject transform="translate( -194.29296875, -43)" height="18.5" width="84.890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+int user_id</span></div></foreignObject><foreignObject transform="translate( -194.29296875, -20.5)" height="18.5" width="71.40625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+str name</span></div></foreignObject><foreignObject transform="translate( -194.29296875, 2)" height="18.5" width="71.9453125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+str email</span></div></foreignObject><foreignObject transform="translate( -194.29296875, 24.5)" height="18.5" width="160.09375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+DateTime created_at</span></div></foreignObject><foreignObject transform="translate( -194.29296875, 59)" height="18.5" width="388.5859375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(user_id: int, name: str, email: str) : -&gt; None</span></div></foreignObject></g></g><g transform="translate(247.27734375, 335.5)" id="classId-UserProfile-1" class="node default"><rect height="147.5" width="478.5546875" y="-73.75" x="-239.27734375" class="outer title-state"/><line y2="-43.25" y1="-43.25" x2="239.27734375" x1="-239.27734375" class="divider"/><line y2="40.25" y1="40.25" x2="239.27734375" x1="-239.27734375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -41.82421875, -66.25)" height="18.5" width="83.6484375" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">UserProfile</span></div></foreignObject><foreignObject transform="translate( -231.77734375, -31.75)" height="18.5" width="84.890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+int user_id</span></div></foreignObject><foreignObject transform="translate( -231.77734375, -9.25)" height="18.5" width="126.265625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+dict preferences</span></div></foreignObject><foreignObject transform="translate( -231.77734375, 13.25)" height="18.5" width="89.7734375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+dict history</span></div></foreignObject><foreignObject transform="translate( -231.77734375, 47.75)" height="18.5" width="463.5546875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(user_id: int, preferences: dict, history: dict) : -&gt; None</span></div></foreignObject></g></g><g transform="translate(1479.5546875, 104.25)" id="classId-Content-2" class="node default"><rect height="192.5" width="740.359375" y="-96.25" x="-370.1796875" class="outer title-state"/><line y2="-65.75" y1="-65.75" x2="370.1796875" x1="-370.1796875" class="divider"/><line y2="62.75" y1="62.75" x2="370.1796875" x1="-370.1796875" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -29.80859375, -88.75)" height="18.5" width="59.6171875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Content</span></div></foreignObject><foreignObject transform="translate( -362.6796875, -54.25)" height="18.5" width="110.1328125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+int content_id</span></div></foreignObject><foreignObject transform="translate( -362.6796875, -31.75)" height="18.5" width="62.9453125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+str title</span></div></foreignObject><foreignObject transform="translate( -362.6796875, -9.25)" height="18.5" width="112.21875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+str description</span></div></foreignObject><foreignObject transform="translate( -362.6796875, 13.25)" height="18.5" width="94.375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+str category</span></div></foreignObject><foreignObject transform="translate( -362.6796875, 35.75)" height="18.5" width="173.546875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+DateTime published_at</span></div></foreignObject><foreignObject transform="translate( -362.6796875, 70.25)" height="18.5" width="725.359375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(content_id: int, title: str, description: str, category: str, published_at: DateTime) : -&gt; None</span></div></foreignObject></g></g><g transform="translate(759.69921875, 335.5)" id="classId-CollaborativeFilteringModel-3" class="node default"><rect height="170" width="446.2890625" y="-85" x="-223.14453125" class="outer title-state"/><line y2="-54.5" y1="-54.5" x2="223.14453125" x1="-223.14453125" class="divider"/><line y2="6.5" y1="6.5" x2="223.14453125" x1="-223.14453125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -103.203125, -77.5)" height="18.5" width="206.40625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">CollaborativeFilteringModel</span></div></foreignObject><foreignObject transform="translate( -215.64453125, -43)" height="18.5" width="123.28125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+DataFrame data</span></div></foreignObject><foreignObject transform="translate( -215.64453125, -20.5)" height="18.5" width="116.7421875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+str model_type</span></div></foreignObject><foreignObject transform="translate( -215.64453125, 14)" height="18.5" width="394.46875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(data: DataFrame, model_type: str) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -215.64453125, 36.5)" height="18.5" width="107.828125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+fit() : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -215.64453125, 59)" height="18.5" width="431.2890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+predict(user_id: int, n_recommendations: int) : -&gt; List[int]</span></div></foreignObject></g></g><g transform="translate(2378.583984375, 335.5)" id="classId-ContentBasedFilteringModel-4" class="node default"><rect height="170" width="446.2890625" y="-85" x="-223.14453125" class="outer title-state"/><line y2="-54.5" y1="-54.5" x2="223.14453125" x1="-223.14453125" class="divider"/><line y2="6.5" y1="6.5" x2="223.14453125" x1="-223.14453125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -105.3359375, -77.5)" height="18.5" width="210.671875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">ContentBasedFilteringModel</span></div></foreignObject><foreignObject transform="translate( -215.64453125, -43)" height="18.5" width="123.28125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+DataFrame data</span></div></foreignObject><foreignObject transform="translate( -215.64453125, -20.5)" height="18.5" width="116.7421875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+str model_type</span></div></foreignObject><foreignObject transform="translate( -215.64453125, 14)" height="18.5" width="394.46875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(data: DataFrame, model_type: str) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -215.64453125, 36.5)" height="18.5" width="107.828125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+fit() : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -215.64453125, 59)" height="18.5" width="431.2890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+predict(user_id: int, n_recommendations: int) : -&gt; List[int]</span></div></foreignObject></g></g><g transform="translate(711.607421875, 566.75)" id="classId-Recommender-5" class="node default"><rect height="192.5" width="1016.46875" y="-96.25" x="-508.234375" class="outer title-state"/><line y2="-65.75" y1="-65.75" x2="508.234375" x1="-508.234375" class="divider"/><line y2="40.25" y1="40.25" x2="508.234375" x1="-508.234375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -53.8359375, -88.75)" height="18.5" width="107.671875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Recommender</span></div></foreignObject><foreignObject transform="translate( -500.734375, -54.25)" height="18.5" width="84.890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+int user_id</span></div></foreignObject><foreignObject transform="translate( -500.734375, -31.75)" height="18.5" width="178.1015625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+UserProfile user_profile</span></div></foreignObject><foreignObject transform="translate( -500.734375, -9.25)" height="18.5" width="277.859375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+CollaborativeFilteringModel cf_model</span></div></foreignObject><foreignObject transform="translate( -500.734375, 13.25)" height="18.5" width="289.8125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+ContentBasedFilteringModel cbf_model</span></div></foreignObject><foreignObject transform="translate( -500.734375, 47.75)" height="18.5" width="1001.46875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(user_id: int, user_profile: UserProfile, cf_model: CollaborativeFilteringModel, cbf_model: ContentBasedFilteringModel) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -500.734375, 70.25)" height="18.5" width="445.625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+get_recommendations(n_recommendations: int) : -&gt; List[int]</span></div></foreignObject></g></g><g transform="translate(448.484375, 775.5)" id="classId-ExperimentationPlatform-6" class="node default"><rect height="125" width="576.8828125" y="-62.5" x="-288.44140625" class="outer title-state"/><line y2="-32" y1="-32" x2="288.44140625" x1="-288.44140625" class="divider"/><line y2="6.5" y1="6.5" x2="288.44140625" x1="-288.44140625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -94.734375, -55)" height="18.5" width="189.46875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">ExperimentationPlatform</span></div></foreignObject><foreignObject transform="translate( -280.94140625, -20.5)" height="18.5" width="258.0546875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+List[Recommender] recommenders</span></div></foreignObject><foreignObject transform="translate( -280.94140625, 14)" height="18.5" width="404.3203125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(recommenders: List[Recommender]) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -280.94140625, 36.5)" height="18.5" width="561.8828125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+run_experiment(user_id: int, n_recommendations: int) : -&gt; Dict[str, List[int]]</span></div></foreignObject></g></g><g transform="translate(974.73046875, 775.5)" id="classId-Optimization-7" class="node default"><rect height="125" width="375.609375" y="-62.5" x="-187.8046875" class="outer title-state"/><line y2="-32" y1="-32" x2="187.8046875" x1="-187.8046875" class="divider"/><line y2="6.5" y1="6.5" x2="187.8046875" x1="-187.8046875" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -48.40234375, -55)" height="18.5" width="96.8046875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Optimization</span></div></foreignObject><foreignObject transform="translate( -180.3046875, -20.5)" height="18.5" width="214.34375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+Recommender recommender</span></div></foreignObject><foreignObject transform="translate( -180.3046875, 14)" height="18.5" width="360.609375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(recommender: Recommender) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -180.3046875, 36.5)" height="18.5" width="153.578125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+optimize() : -&gt; None</span></div></foreignObject></g></g><g transform="translate(1256.75, 335.5)" id="classId-Feedback-8" class="node default"><rect height="147.5" width="447.8125" y="-73.75" x="-223.90625" class="outer title-state"/><line y2="-43.25" y1="-43.25" x2="223.90625" x1="-223.90625" class="divider"/><line y2="40.25" y1="40.25" x2="223.90625" x1="-223.90625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -35.5234375, -66.25)" height="18.5" width="71.046875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Feedback</span></div></foreignObject><foreignObject transform="translate( -216.40625, -31.75)" height="18.5" width="84.890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+int user_id</span></div></foreignObject><foreignObject transform="translate( -216.40625, -9.25)" height="18.5" width="110.1328125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+int content_id</span></div></foreignObject><foreignObject transform="translate( -216.40625, 13.25)" height="18.5" width="75.1640625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+int rating</span></div></foreignObject><foreignObject transform="translate( -216.40625, 47.75)" height="18.5" width="432.8125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(user_id: int, content_id: int, rating: int) : -&gt; None</span></div></foreignObject></g></g><g transform="translate(1400.33984375, 775.5)" id="classId-Monitoring-9" class="node default"><rect height="125" width="375.609375" y="-62.5" x="-187.8046875" class="outer title-state"/><line y2="-32" y1="-32" x2="187.8046875" x1="-187.8046875" class="divider"/><line y2="6.5" y1="6.5" x2="187.8046875" x1="-187.8046875" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -39.83984375, -55)" height="18.5" width="79.6796875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Monitoring</span></div></foreignObject><foreignObject transform="translate( -180.3046875, -20.5)" height="18.5" width="214.34375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+Recommender recommender</span></div></foreignObject><foreignObject transform="translate( -180.3046875, 14)" height="18.5" width="360.609375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(recommender: Recommender) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -180.3046875, 36.5)" height="18.5" width="208.328125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+generate_report() : -&gt; None</span></div></foreignObject></g></g><g transform="translate(2122.76171875, 104.25)" id="classId-Advertising-10" class="node default"><rect height="125" width="446.0546875" y="-62.5" x="-223.02734375" class="outer title-state"/><line y2="-32" y1="-32" x2="223.02734375" x1="-223.02734375" class="divider"/><line y2="29" y1="29" x2="223.02734375" x1="-223.02734375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -42.07421875, -55)" height="18.5" width="84.1484375" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Advertising</span></div></foreignObject><foreignObject transform="translate( -215.52734375, -20.5)" height="18.5" width="127.15625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+int advertiser_id</span></div></foreignObject><foreignObject transform="translate( -215.52734375, 2)" height="18.5" width="149.453125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+str target_audience</span></div></foreignObject><foreignObject transform="translate( -215.52734375, 36.5)" height="18.5" width="431.0546875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(advertiser_id: int, target_audience: str) : -&gt; None</span></div></foreignObject></g></g><g transform="translate(1987.220703125, 335.5)" id="classId-Privacy-11" class="node default"><rect height="125" width="236.4375" y="-62.5" x="-118.21875" class="outer title-state"/><line y2="-32" y1="-32" x2="118.21875" x1="-118.21875" class="divider"/><line y2="6.5" y1="6.5" x2="118.21875" x1="-118.21875" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -27.34375, -55)" height="18.5" width="54.6875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Privacy</span></div></foreignObject><foreignObject transform="translate( -110.71875, -20.5)" height="18.5" width="75.171875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+User user</span></div></foreignObject><foreignObject transform="translate( -110.71875, 14)" height="18.5" width="221.4375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(user: User) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -110.71875, 36.5)" height="18.5" width="198.78125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+ensure_privacy() : -&gt; None</span></div></foreignObject></g></g></g></g></g></svg>