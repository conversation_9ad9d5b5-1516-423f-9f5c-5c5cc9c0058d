<svg aria-roledescription="classDiagram" role="graphics-document document" viewBox="0 0 2628.19140625 708" style="max-width: 2628.19px; background-color: white;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:2px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg g.classGroup text{fill:#9370DB;fill:#131300;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#my-svg g.classGroup text .title{font-weight:bolder;}#my-svg .nodeLabel,#my-svg .edgeLabel{color:#131300;}#my-svg .edgeLabel .label rect{fill:#ECECFF;}#my-svg .label text{fill:#131300;}#my-svg .edgeLabel .label span{background:#ECECFF;}#my-svg .classTitle{font-weight:bolder;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .divider{stroke:#9370DB;stroke-width:1;}#my-svg g.clickable{cursor:pointer;}#my-svg g.classGroup rect{fill:#ECECFF;stroke:#9370DB;}#my-svg g.classGroup line{stroke:#9370DB;stroke-width:1;}#my-svg .classLabel .box{stroke:none;stroke-width:0;fill:#ECECFF;opacity:0.5;}#my-svg .classLabel .label{fill:#9370DB;font-size:10px;}#my-svg .relation{stroke:#333333;stroke-width:1;fill:none;}#my-svg .dashed-line{stroke-dasharray:3;}#my-svg .dotted-line{stroke-dasharray:1 2;}#my-svg #compositionStart,#my-svg .composition{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #compositionEnd,#my-svg .composition{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #extensionStart,#my-svg .extension{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #extensionEnd,#my-svg .extension{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #aggregationStart,#my-svg .aggregation{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #aggregationEnd,#my-svg .aggregation{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #lollipopStart,#my-svg .lollipop{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #lollipopEnd,#my-svg .lollipop{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg .edgeTerminals{font-size:11px;}#my-svg .classTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker aggregation classDiagram" id="classDiagram-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker aggregation classDiagram" id="classDiagram-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker extension classDiagram" id="classDiagram-extensionStart"><path d="M 1,7 L18,13 V 1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker extension classDiagram" id="classDiagram-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker composition classDiagram" id="classDiagram-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker composition classDiagram" id="classDiagram-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker dependency classDiagram" id="classDiagram-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker dependency classDiagram" id="classDiagram-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker lollipop classDiagram" id="classDiagram-lollipopStart"><circle r="6" cy="7" cx="6" fill="white" stroke="black"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path style="fill:none" class="edge-pattern-solid relation" id="id1" d="M901.921875,178L901.921875,187.45833333333334C901.921875,196.91666666666666,901.921875,215.83333333333334,901.921875,234.75C901.921875,253.66666666666666,901.921875,272.5833333333333,901.921875,282.0416666666667L901.921875,291.5"/><path style="fill:none" class="edge-pattern-solid relation" id="id2" d="M291.48046875,461.5L291.48046875,467.2083333333333C291.48046875,472.9166666666667,291.48046875,484.3333333333333,293.1300663871419,495.75C294.7796640242837,507.1666666666667,298.07885929856747,518.5833333333334,299.7284569357093,524.2916666666666L301.37805457285117,530"/><path style="fill:none" class="edge-pattern-solid relation" id="id3" d="M731.2789713541666,439L709.3942599826388,448.4583333333333C687.509548611111,457.9166666666667,643.7401258680555,476.8333333333333,608.7380093002052,492C573.735892732355,507.1666666666667,547.50108233971,518.5833333333334,534.3836771433874,524.2916666666666L521.266271947065,530"/><path style="fill:none" class="edge-pattern-solid relation" id="id4" d="M920.0701852849617,439L922.3976804458015,448.4583333333333C924.7251756066411,457.9166666666667,929.3801659283205,476.8333333333333,931.7076610891603,493.875C934.03515625,510.9166666666667,934.03515625,526.0833333333334,934.03515625,533.6666666666666L934.03515625,541.25"/></g><g class="edgeLabels"><g transform="translate(901.921875, 234.75)" class="edgeLabel"><g transform="translate(-21.87890625, -9.25)" class="label"><foreignObject height="18.5" width="43.7578125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><span class="edgeLabel">config</span></span></div></foreignObject></g></g><g transform="translate(886.921875, 195.5)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(911.921875, 269)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(291.48046875, 495.75)" class="edgeLabel"><g transform="translate(-26.80859375, -9.25)" class="label"><foreignObject height="18.5" width="53.6171875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><span class="edgeLabel">dataset</span></span></div></foreignObject></g></g><g transform="translate(276.58484248888595, 479.0745679684012)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(305.9300477300069, 504.02359833704423)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(599.970703125, 495.75)" class="edgeLabel"><g transform="translate(-22.11328125, -9.25)" class="label"><foreignObject height="18.5" width="44.2265625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><span class="edgeLabel">model</span></span></div></foreignObject></g></g><g transform="translate(709.2642051300562, 432.1735757458411)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(538.298110462477, 531.7711218886388)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(934.03515625, 495.75)" class="edgeLabel"><g transform="translate(-22.11328125, -9.25)" class="label"><foreignObject height="18.5" width="44.2265625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><span class="edgeLabel">model</span></span></div></foreignObject></g></g><g transform="translate(909.6863390295518, 459.57731121755245)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(944.03515625, 518.75)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g class="nodes"><g transform="translate(901.921875, 104.25)" id="classId-ModelConfig-0" class="node default"><rect height="147.5" width="529.4921875" y="-73.75" x="-264.74609375" class="outer title-state"/><line y2="-43.25" y1="-43.25" x2="264.74609375" x1="-264.74609375" class="divider"/><line y2="40.25" y1="40.25" x2="264.74609375" x1="-264.74609375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -45.59375, -66.25)" height="18.5" width="91.1875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">ModelConfig</span></div></foreignObject><foreignObject transform="translate( -257.24609375, -31.75)" height="18.5" width="129.8984375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+model_name: str</span></div></foreignObject><foreignObject transform="translate( -257.24609375, -9.25)" height="18.5" width="122.6171875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+model_type: str</span></div></foreignObject><foreignObject transform="translate( -257.24609375, 13.25)" height="18.5" width="155.875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+config: Dict[str, Any]</span></div></foreignObject><foreignObject transform="translate( -257.24609375, 47.75)" height="18.5" width="514.4921875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, model_name: str, model_type: str, config: Dict[str, Any])</span></div></foreignObject></g></g><g transform="translate(291.48046875, 365.25)" id="classId-Dataset-1" class="node default"><rect height="192.5" width="560.9296875" y="-96.25" x="-280.46484375" class="outer title-state"/><line y2="-65.75" y1="-65.75" x2="280.46484375" x1="-280.46484375" class="divider"/><line y2="17.75" y1="17.75" x2="280.46484375" x1="-280.46484375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -28.05078125, -88.75)" height="18.5" width="56.1015625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Dataset</span></div></foreignObject><foreignObject transform="translate( -272.96484375, -54.25)" height="18.5" width="139.2890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+dataset_name: str</span></div></foreignObject><foreignObject transform="translate( -272.96484375, -31.75)" height="18.5" width="69.140625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+split: str</span></div></foreignObject><foreignObject transform="translate( -272.96484375, -9.25)" height="18.5" width="231.3984375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+tokenizer: PreTrainedTokenizer</span></div></foreignObject><foreignObject transform="translate( -272.96484375, 25.25)" height="18.5" width="545.9296875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, dataset_name: str, split: str, tokenizer: PreTrainedTokenizer)</span></div></foreignObject><foreignObject transform="translate( -272.96484375, 47.75)" height="18.5" width="206.1796875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+load_data(self) : -&gt; Dataset</span></div></foreignObject><foreignObject transform="translate( -272.96484375, 70.25)" height="18.5" width="377.171875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+preprocess_data(self, max_length: int) : -&gt; Dataset</span></div></foreignObject></g></g><g transform="translate(901.921875, 365.25)" id="classId-BaseModel-2" class="node default"><rect height="147.5" width="435.1875" y="-73.75" x="-217.59375" class="outer title-state"/><line y2="-43.25" y1="-43.25" x2="217.59375" x1="-217.59375" class="divider"/><line y2="17.75" y1="17.75" x2="217.59375" x1="-217.59375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -39.15625, -66.25)" height="18.5" width="78.3125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">BaseModel</span></div></foreignObject><foreignObject transform="translate( -210.09375, -31.75)" height="18.5" width="203.1640625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+model_config: ModelConfig</span></div></foreignObject><foreignObject transform="translate( -210.09375, -9.25)" height="18.5" width="319.6796875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+model: Union[PreTrainedModel, nn.Module]</span></div></foreignObject><foreignObject transform="translate( -210.09375, 25.25)" height="18.5" width="309.21875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, model_config: ModelConfig)</span></div></foreignObject><foreignObject transform="translate( -210.09375, 47.75)" height="18.5" width="420.1875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+load_model(self) : -&gt; Union[PreTrainedModel, nn.Module]</span></div></foreignObject></g></g><g transform="translate(325.94140625, 615)" id="classId-FineTuningPipeline-3" class="node default"><rect height="170" width="635.8828125" y="-85" x="-317.94140625" class="outer title-state"/><line y2="-54.5" y1="-54.5" x2="317.94140625" x1="-317.94140625" class="divider"/><line y2="29" y1="29" x2="317.94140625" x1="-317.94140625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -71.52734375, -77.5)" height="18.5" width="143.0546875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">FineTuningPipeline</span></div></foreignObject><foreignObject transform="translate( -310.44140625, -43)" height="18.5" width="138.2734375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+model: BaseModel</span></div></foreignObject><foreignObject transform="translate( -310.44140625, -20.5)" height="18.5" width="127.21875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+dataset: Dataset</span></div></foreignObject><foreignObject transform="translate( -310.44140625, 2)" height="18.5" width="244.7265625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+training_args: TrainingArguments</span></div></foreignObject><foreignObject transform="translate( -310.44140625, 36.5)" height="18.5" width="620.8828125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, model: BaseModel, dataset: Dataset, training_args: TrainingArguments)</span></div></foreignObject><foreignObject transform="translate( -310.44140625, 59)" height="18.5" width="316.484375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+train(self) : -&gt; Tuple[Trainer, Dict[str, Any]]</span></div></foreignObject></g></g><g transform="translate(1408.1953125, 104.25)" id="classId-Experiment-4" class="node default"><rect height="192.5" width="383.0546875" y="-96.25" x="-191.52734375" class="outer title-state"/><line y2="-65.75" y1="-65.75" x2="191.52734375" x1="-191.52734375" class="divider"/><line y2="-4.75" y1="-4.75" x2="191.52734375" x1="-191.52734375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -43.3984375, -88.75)" height="18.5" width="86.796875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Experiment</span></div></foreignObject><foreignObject transform="translate( -184.02734375, -54.25)" height="18.5" width="77.28125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+name: str</span></div></foreignObject><foreignObject transform="translate( -184.02734375, -31.75)" height="18.5" width="118.09375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+description: str</span></div></foreignObject><foreignObject transform="translate( -184.02734375, 2.75)" height="18.5" width="301.453125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, name: str, description: str)</span></div></foreignObject><foreignObject transform="translate( -184.02734375, 25.25)" height="18.5" width="150.6328125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+start(self) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -184.02734375, 47.75)" height="18.5" width="368.0546875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+log_metrics(self, metrics: Dict[str, Any]) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -184.02734375, 70.25)" height="18.5" width="143.2265625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+end(self) : -&gt; None</span></div></foreignObject></g></g><g transform="translate(1850.43359375, 104.25)" id="classId-Artifact-5" class="node default"><rect height="192.5" width="401.421875" y="-96.25" x="-200.7109375" class="outer title-state"/><line y2="-65.75" y1="-65.75" x2="200.7109375" x1="-200.7109375" class="divider"/><line y2="17.75" y1="17.75" x2="200.7109375" x1="-200.7109375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -28.52734375, -88.75)" height="18.5" width="57.0546875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Artifact</span></div></foreignObject><foreignObject transform="translate( -193.2109375, -54.25)" height="18.5" width="77.28125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+name: str</span></div></foreignObject><foreignObject transform="translate( -193.2109375, -31.75)" height="18.5" width="132.5078125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+artifact_type: str</span></div></foreignObject><foreignObject transform="translate( -193.2109375, -9.25)" height="18.5" width="70.53125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+path: str</span></div></foreignObject><foreignObject transform="translate( -193.2109375, 25.25)" height="18.5" width="386.421875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, name: str, artifact_type: str, path: str)</span></div></foreignObject><foreignObject transform="translate( -193.2109375, 47.75)" height="18.5" width="148.2890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+save(self) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -193.2109375, 70.25)" height="18.5" width="136.390625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+load(self) : -&gt; Any</span></div></foreignObject></g></g><g transform="translate(934.03515625, 615)" id="classId-ModelRegistry-6" class="node default"><rect height="147.5" width="471.921875" y="-73.75" x="-235.9609375" class="outer title-state"/><line y2="-43.25" y1="-43.25" x2="235.9609375" x1="-235.9609375" class="divider"/><line y2="-27.25" y1="-27.25" x2="235.9609375" x1="-235.9609375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -52.28125, -66.25)" height="18.5" width="104.5625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">ModelRegistry</span></div></foreignObject><foreignObject transform="translate( -228.4609375, -19.75)" height="18.5" width="103.75"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self)</span></div></foreignObject><foreignObject transform="translate( -228.4609375, 2.75)" height="18.5" width="456.921875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+register_model(self, model: BaseModel, version: str) : -&gt; None</span></div></foreignObject><foreignObject transform="translate( -228.4609375, 25.25)" height="18.5" width="452.7578125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+get_model(self, model_name: str, version: str) : -&gt; BaseModel</span></div></foreignObject><foreignObject transform="translate( -228.4609375, 47.75)" height="18.5" width="438.7109375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+deploy_model(self, model_name: str, version: str) : -&gt; None</span></div></foreignObject></g></g><g transform="translate(2360.66796875, 104.25)" id="classId-ModelMonitoring-7" class="node default"><rect height="125" width="519.046875" y="-62.5" x="-259.5234375" class="outer title-state"/><line y2="-32" y1="-32" x2="259.5234375" x1="-259.5234375" class="divider"/><line y2="6.5" y1="6.5" x2="259.5234375" x1="-259.5234375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -61.9296875, -55)" height="18.5" width="123.859375" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">ModelMonitoring</span></div></foreignObject><foreignObject transform="translate( -252.0234375, -20.5)" height="18.5" width="225.3828125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+model_registry: ModelRegistry</span></div></foreignObject><foreignObject transform="translate( -252.0234375, 14)" height="18.5" width="331.4375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, model_registry: ModelRegistry)</span></div></foreignObject><foreignObject transform="translate( -252.0234375, 36.5)" height="18.5" width="504.046875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+monitor_model(self, model_name: str, version: str) : -&gt; Dict[str, Any]</span></div></foreignObject></g></g></g></g></g></svg>