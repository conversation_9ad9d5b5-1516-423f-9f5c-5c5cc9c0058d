<svg aria-roledescription="classDiagram" role="graphics-document document" viewBox="0 0 2742.859375 558.5" style="max-width: 2742.86px; background-color: white;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:2px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg g.classGroup text{fill:#9370DB;fill:#131300;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#my-svg g.classGroup text .title{font-weight:bolder;}#my-svg .nodeLabel,#my-svg .edgeLabel{color:#131300;}#my-svg .edgeLabel .label rect{fill:#ECECFF;}#my-svg .label text{fill:#131300;}#my-svg .edgeLabel .label span{background:#ECECFF;}#my-svg .classTitle{font-weight:bolder;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .divider{stroke:#9370DB;stroke-width:1;}#my-svg g.clickable{cursor:pointer;}#my-svg g.classGroup rect{fill:#ECECFF;stroke:#9370DB;}#my-svg g.classGroup line{stroke:#9370DB;stroke-width:1;}#my-svg .classLabel .box{stroke:none;stroke-width:0;fill:#ECECFF;opacity:0.5;}#my-svg .classLabel .label{fill:#9370DB;font-size:10px;}#my-svg .relation{stroke:#333333;stroke-width:1;fill:none;}#my-svg .dashed-line{stroke-dasharray:3;}#my-svg .dotted-line{stroke-dasharray:1 2;}#my-svg #compositionStart,#my-svg .composition{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #compositionEnd,#my-svg .composition{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #extensionStart,#my-svg .extension{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #extensionEnd,#my-svg .extension{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #aggregationStart,#my-svg .aggregation{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #aggregationEnd,#my-svg .aggregation{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #lollipopStart,#my-svg .lollipop{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #lollipopEnd,#my-svg .lollipop{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg .edgeTerminals{font-size:11px;}#my-svg .classTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker aggregation classDiagram" id="classDiagram-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker aggregation classDiagram" id="classDiagram-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker extension classDiagram" id="classDiagram-extensionStart"><path d="M 1,7 L18,13 V 1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker extension classDiagram" id="classDiagram-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker composition classDiagram" id="classDiagram-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker composition classDiagram" id="classDiagram-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker dependency classDiagram" id="classDiagram-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker dependency classDiagram" id="classDiagram-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker lollipop classDiagram" id="classDiagram-lollipopStart"><circle r="6" cy="7" cx="6" fill="white" stroke="black"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path style="fill:none" class="edge-pattern-solid relation" id="id1" d="M1289.734375,107.71438321881968L1103.82421875,123.59531934901639C917.9140625,139.4762554792131,546.09375,171.23812773960654,360.18359375,191.28573053646994C174.2734375,211.33333333333334,174.2734375,219.66666666666666,174.2734375,223.83333333333334L174.2734375,228"/><path style="fill:none" class="edge-pattern-solid relation" id="id2" d="M1289.734375,116.72882433800832L1185.3567708333333,131.10735361500693C1080.9791666666667,145.48588289200555,872.2239583333334,174.24294144600276,767.8463541666666,196.53813738966804C663.46875,218.83333333333334,663.46875,234.66666666666666,663.46875,242.58333333333334L663.46875,250.5"/><path style="fill:none" class="edge-pattern-solid relation" id="id3" d="M1634.2421875,109.93758402150951L1791.9817708333333,125.44798668459127C1949.7213541666667,140.958389347673,2265.2005208333335,171.97919467383653,2422.9401041666665,191.65626400358494C2580.6796875,211.33333333333334,2580.6796875,219.66666666666666,2580.6796875,223.83333333333334L2580.6796875,228"/><path style="fill:none" class="edge-pattern-solid relation" id="id4" d="M1289.734375,111.65577730001654L1149.1666666666667,126.87981441668045C1008.5989583333334,142.10385153334437,727.4635416666666,172.55192576667218,586.8958333333334,206.10929621666943C446.328125,239.66666666666666,446.328125,276.3333333333333,446.328125,313C446.328125,349.6666666666667,446.328125,386.3333333333333,450.19595286885243,408.8333333333333C454.0637807377049,431.3333333333333,461.7994364754098,439.6666666666667,465.66726434426226,443.8333333333333L469.53509221311475,448"/><path style="fill:none" class="edge-pattern-solid relation" id="id5" d="M1289.734375,145.17737858333783L1257.9192708333333,154.81448215278152C1226.1041666666667,164.45158572222522,1162.4739583333333,183.7257928611126,1130.6588541666667,199.404563097223C1098.84375,215.08333333333334,1098.84375,227.16666666666666,1098.84375,233.20833333333334L1098.84375,239.25"/><path style="fill:none" class="edge-pattern-solid relation" id="id6" d="M1461.98828125,178L1461.98828125,182.16666666666666C1461.98828125,186.33333333333334,1461.98828125,194.66666666666666,1461.98828125,206.75C1461.98828125,218.83333333333334,1461.98828125,234.66666666666666,1461.98828125,242.58333333333334L1461.98828125,250.5"/><path style="fill:none" class="edge-pattern-solid relation" id="id7" d="M1634.2421875,163.22425225120884L1650.5032552083333,169.85354354267403C1666.7643229166667,176.48283483413923,1699.2864583333333,189.74141741706958,1715.5475260416667,202.41237537520146C1731.80859375,215.08333333333334,1731.80859375,227.16666666666666,1731.80859375,233.20833333333334L1731.80859375,239.25"/><path style="fill:none" class="edge-pattern-solid relation" id="id8" d="M1634.2421875,128.69818957904033L1693.9967447916667,141.08182464920029C1753.7513020833333,153.4654597193602,1873.2604166666667,178.23272985968012,1933.0149739583333,200.40803159650673C1992.76953125,222.58333333333334,1992.76953125,242.16666666666666,1992.76953125,251.95833333333334L1992.76953125,261.75"/><path style="fill:none" class="edge-pattern-solid relation" id="id9" d="M1634.2421875,116.73915979875889L1738.5618489583333,131.11596649896575C1842.8815104166667,145.4927731991726,2051.5208333333335,174.24638659958632,2155.8404947916665,194.66485996645983C2260.16015625,215.08333333333334,2260.16015625,227.16666666666666,2260.16015625,233.20833333333334L2260.16015625,239.25"/><path style="fill:none" class="edge-pattern-solid relation" id="id10" d="M2580.6796875,398L2580.6796875,402.1666666666667C2580.6796875,406.3333333333333,2580.6796875,414.6666666666667,2580.6796875,423C2580.6796875,431.3333333333333,2580.6796875,439.6666666666667,2580.6796875,443.8333333333333L2580.6796875,448"/><path style="fill:none" class="edge-pattern-solid relation" id="id11" d="M174.2734375,398L174.2734375,402.1666666666667C174.2734375,406.3333333333333,174.2734375,414.6666666666667,174.2734375,423C174.2734375,431.3333333333333,174.2734375,439.6666666666667,174.2734375,443.8333333333333L174.2734375,448"/><path style="fill:none" class="edge-pattern-solid relation" id="id12" d="M340.546875,326.06423216323526L546.169921875,342.22019346936275C751.79296875,358.3761547754902,1163.0390625,390.6880773877451,1215.28125,417.906806811314C1267.5234375,445.12553623488293,960.76171875,467.2510724697658,807.380859375,478.31384058720727L654,489.3766087046487"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1271.0211866407963, 94.25828491979686)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(188.25167029789645, 215.46901311681086)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1270.3510993607558, 104.25731177965842)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(673.46875, 228)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1650.1903370316882, 126.57809262295889)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2584.711038622697, 204.64470507956295)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">*</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1270.7210017567945, 98.62728399056)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(464.1009695136297, 420.0432607033238)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">*</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1268.6373622730916, 135.89481137374176)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1108.84375, 216.75)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1446.98828125, 195.5)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1471.98828125, 228)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1644.7845818519934, 183.72078662478188)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1741.80859375, 216.75)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1648.334125443714, 146.93735855656)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2002.76953125, 239.25)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1649.5304617234456, 133.98788935396752)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2270.16015625, 216.75)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(2565.6796875, 415.5)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2590.6796875, 425.5)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">*</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(159.2734375, 415.5)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(184.2734375, 425.5)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">*</span></div></foreignObject></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(356.8181663927129, 342.38890907928555)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(667.5337465818615, 498.0788069822733)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">*</span></div></foreignObject></g></g><g class="nodes"><g transform="translate(1461.98828125, 93)" id="classId-Game-0" class="node default"><rect height="170" width="344.5078125" y="-85" x="-172.25390625" class="outer title-state"/><line y2="-54.5" y1="-54.5" x2="172.25390625" x1="-172.25390625" class="divider"/><line y2="-38.5" y1="-38.5" x2="172.25390625" x1="-172.25390625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -21.10546875, -77.5)" height="18.5" width="42.2109375" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Game</span></div></foreignObject><foreignObject transform="translate( -164.75390625, -31)" height="18.5" width="187.1015625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, user: User)</span></div></foreignObject><foreignObject transform="translate( -164.75390625, -8.5)" height="18.5" width="79.765625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+start(self)</span></div></foreignObject><foreignObject transform="translate( -164.75390625, 14)" height="18.5" width="213.25"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+play_level(self, level: Level)</span></div></foreignObject><foreignObject transform="translate( -164.75390625, 36.5)" height="18.5" width="329.5078125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+complete_level(self, level: Level, score: int)</span></div></foreignObject><foreignObject transform="translate( -164.75390625, 59)" height="18.5" width="303.3984375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+use_power_up(self, power_up: PowerUp)</span></div></foreignObject></g></g><g transform="translate(174.2734375, 313)" id="classId-User-1" class="node default"><rect height="170" width="332.546875" y="-85" x="-166.2734375" class="outer title-state"/><line y2="-54.5" y1="-54.5" x2="166.2734375" x1="-166.2734375" class="divider"/><line y2="-38.5" y1="-38.5" x2="166.2734375" x1="-166.2734375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -16.8828125, -77.5)" height="18.5" width="33.765625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">User</span></div></foreignObject><foreignObject transform="translate( -158.7734375, -31)" height="18.5" width="317.546875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, username: str, password: str)</span></div></foreignObject><foreignObject transform="translate( -158.7734375, -8.5)" height="18.5" width="80.6171875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+login(self)</span></div></foreignObject><foreignObject transform="translate( -158.7734375, 14)" height="18.5" width="101.28125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+register(self)</span></div></foreignObject><foreignObject transform="translate( -158.7734375, 36.5)" height="18.5" width="303.8984375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+connect_social(self, social_platform: str)</span></div></foreignObject><foreignObject transform="translate( -158.7734375, 59)" height="18.5" width="230.65625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+get_friends(self) : -&gt; List[User]</span></div></foreignObject></g></g><g transform="translate(2580.6796875, 313)" id="classId-Level-2" class="node default"><rect height="170" width="308.359375" y="-85" x="-154.1796875" class="outer title-state"/><line y2="-54.5" y1="-54.5" x2="154.1796875" x1="-154.1796875" class="divider"/><line y2="-38.5" y1="-38.5" x2="154.1796875" x1="-154.1796875" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -20.1953125, -77.5)" height="18.5" width="40.390625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Level</span></div></foreignObject><foreignObject transform="translate( -146.6796875, -31)" height="18.5" width="293.359375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, level_data: Dict[str, Any])</span></div></foreignObject><foreignObject transform="translate( -146.6796875, -8.5)" height="18.5" width="76.6015625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+load(self)</span></div></foreignObject><foreignObject transform="translate( -146.6796875, 14)" height="18.5" width="109.8984375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+generate(self)</span></div></foreignObject><foreignObject transform="translate( -146.6796875, 36.5)" height="18.5" width="186.3671875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+check_win(self) : -&gt; bool</span></div></foreignObject><foreignObject transform="translate( -146.6796875, 59)" height="18.5" width="292.9453125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+get_objectives(self) : -&gt; List[Objective]</span></div></foreignObject></g></g><g transform="translate(2580.6796875, 499.25)" id="classId-Objective-3" class="node default"><rect height="102.5" width="273.8828125" y="-51.25" x="-136.94140625" class="outer title-state"/><line y2="-20.75" y1="-20.75" x2="136.94140625" x1="-136.94140625" class="divider"/><line y2="-4.75" y1="-4.75" x2="136.94140625" x1="-136.94140625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -36.27734375, -43.75)" height="18.5" width="72.5546875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Objective</span></div></foreignObject><foreignObject transform="translate( -129.44140625, 2.75)" height="18.5" width="258.8828125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, type: str, target: int)</span></div></foreignObject><foreignObject transform="translate( -129.44140625, 25.25)" height="18.5" width="198.03125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+is_complete(self) : -&gt; bool</span></div></foreignObject></g></g><g transform="translate(174.2734375, 499.25)" id="classId-PowerUp-4" class="node default"><rect height="102.5" width="311.890625" y="-51.25" x="-155.9453125" class="outer title-state"/><line y2="-20.75" y1="-20.75" x2="155.9453125" x1="-155.9453125" class="divider"/><line y2="-4.75" y1="-4.75" x2="155.9453125" x1="-155.9453125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -33.21484375, -43.75)" height="18.5" width="66.4296875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">PowerUp</span></div></foreignObject><foreignObject transform="translate( -148.4453125, 2.75)" height="18.5" width="296.890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, type: str, effect: Callable)</span></div></foreignObject><foreignObject transform="translate( -148.4453125, 25.25)" height="18.5" width="185.890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+apply(self, game: Game)</span></div></foreignObject></g></g><g transform="translate(517.109375, 499.25)" id="classId-Reward-5" class="node default"><rect height="102.5" width="273.78125" y="-51.25" x="-136.890625" class="outer title-state"/><line y2="-20.75" y1="-20.75" x2="136.890625" x1="-136.890625" class="divider"/><line y2="-4.75" y1="-4.75" x2="136.890625" x1="-136.890625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -28.078125, -43.75)" height="18.5" width="56.15625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Reward</span></div></foreignObject><foreignObject transform="translate( -129.390625, 2.75)" height="18.5" width="258.78125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, type: str, value: Any)</span></div></foreignObject><foreignObject transform="translate( -129.390625, 25.25)" height="18.5" width="168.21875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+claim(self, user: User)</span></div></foreignObject></g></g><g transform="translate(663.46875, 313)" id="classId-UI-6" class="node default"><rect height="125" width="364.28125" y="-62.5" x="-182.140625" class="outer title-state"/><line y2="-32" y1="-32" x2="182.140625" x1="-182.140625" class="divider"/><line y2="-16" y1="-16" x2="182.140625" x1="-182.140625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -7.6484375, -55)" height="18.5" width="15.296875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">UI</span></div></foreignObject><foreignObject transform="translate( -174.640625, -8.5)" height="18.5" width="204.8203125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, game: Game)</span></div></foreignObject><foreignObject transform="translate( -174.640625, 14)" height="18.5" width="93.5234375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+render(self)</span></div></foreignObject><foreignObject transform="translate( -174.640625, 36.5)" height="18.5" width="349.28125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+handle_input(self, event: pygame.event.Event)</span></div></foreignObject></g></g><g transform="translate(1098.84375, 313)" id="classId-Social-7" class="node default"><rect height="147.5" width="406.46875" y="-73.75" x="-203.234375" class="outer title-state"/><line y2="-43.25" y1="-43.25" x2="203.234375" x1="-203.234375" class="divider"/><line y2="-27.25" y1="-27.25" x2="203.234375" x1="-203.234375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -21.71875, -66.25)" height="18.5" width="43.4375" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Social</span></div></foreignObject><foreignObject transform="translate( -195.734375, -19.75)" height="18.5" width="187.1015625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, user: User)</span></div></foreignObject><foreignObject transform="translate( -195.734375, 2.75)" height="18.5" width="280.9140625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+share(self, platform: str, content: str)</span></div></foreignObject><foreignObject transform="translate( -195.734375, 25.25)" height="18.5" width="183.015625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+invite(self, friend: User)</span></div></foreignObject><foreignObject transform="translate( -195.734375, 47.75)" height="18.5" width="391.46875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+compare_scores(self, friend: User) : -&gt; Tuple[int, int]</span></div></foreignObject></g></g><g transform="translate(1461.98828125, 313)" id="classId-Platform-8" class="node default"><rect height="125" width="219.8203125" y="-62.5" x="-109.91015625" class="outer title-state"/><line y2="-32" y1="-32" x2="109.91015625" x1="-109.91015625" class="divider"/><line y2="-16" y1="-16" x2="109.91015625" x1="-109.91015625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -32.265625, -55)" height="18.5" width="64.53125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Platform</span></div></foreignObject><foreignObject transform="translate( -102.41015625, -8.5)" height="18.5" width="204.8203125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, game: Game)</span></div></foreignObject><foreignObject transform="translate( -102.41015625, 14)" height="18.5" width="160.7734375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+save(self, user: User)</span></div></foreignObject><foreignObject transform="translate( -102.41015625, 36.5)" height="18.5" width="159.953125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+load(self, user: User)</span></div></foreignObject></g></g><g transform="translate(1731.80859375, 313)" id="classId-Tutorial-9" class="node default"><rect height="147.5" width="219.8203125" y="-73.75" x="-109.91015625" class="outer title-state"/><line y2="-43.25" y1="-43.25" x2="109.91015625" x1="-109.91015625" class="divider"/><line y2="-27.25" y1="-27.25" x2="109.91015625" x1="-109.91015625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -29.0234375, -66.25)" height="18.5" width="58.046875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Tutorial</span></div></foreignObject><foreignObject transform="translate( -102.41015625, -19.75)" height="18.5" width="204.8203125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, game: Game)</span></div></foreignObject><foreignObject transform="translate( -102.41015625, 2.75)" height="18.5" width="79.765625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+start(self)</span></div></foreignObject><foreignObject transform="translate( -102.41015625, 25.25)" height="18.5" width="116.65625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+next_step(self)</span></div></foreignObject><foreignObject transform="translate( -102.41015625, 47.75)" height="18.5" width="113.1953125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+complete(self)</span></div></foreignObject></g></g><g transform="translate(1992.76953125, 313)" id="classId-IAP-10" class="node default"><rect height="102.5" width="202.1015625" y="-51.25" x="-101.05078125" class="outer title-state"/><line y2="-20.75" y1="-20.75" x2="101.05078125" x1="-101.05078125" class="divider"/><line y2="-4.75" y1="-4.75" x2="101.05078125" x1="-101.05078125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -11.98828125, -43.75)" height="18.5" width="23.9765625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">IAP</span></div></foreignObject><foreignObject transform="translate( -93.55078125, 2.75)" height="18.5" width="187.1015625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, user: User)</span></div></foreignObject><foreignObject transform="translate( -93.55078125, 25.25)" height="18.5" width="183.46875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+purchase(self, item: str)</span></div></foreignObject></g></g><g transform="translate(2260.16015625, 313)" id="classId-Update-11" class="node default"><rect height="147.5" width="232.6796875" y="-73.75" x="-116.33984375" class="outer title-state"/><line y2="-43.25" y1="-43.25" x2="116.33984375" x1="-116.33984375" class="divider"/><line y2="-27.25" y1="-27.25" x2="116.33984375" x1="-116.33984375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -26.7578125, -66.25)" height="18.5" width="53.515625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Update</span></div></foreignObject><foreignObject transform="translate( -108.83984375, -19.75)" height="18.5" width="204.8203125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, game: Game)</span></div></foreignObject><foreignObject transform="translate( -108.83984375, 2.75)" height="18.5" width="217.6796875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+check_updates(self) : -&gt; bool</span></div></foreignObject><foreignObject transform="translate( -108.83984375, 25.25)" height="18.5" width="173.1875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+download_update(self)</span></div></foreignObject><foreignObject transform="translate( -108.83984375, 47.75)" height="18.5" width="143.2578125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+apply_update(self)</span></div></foreignObject></g></g></g></g></g></svg>