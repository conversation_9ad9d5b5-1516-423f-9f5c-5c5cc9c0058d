<svg aria-labelledby="chart-title-my-svg" aria-roledescription="quadrantChart" role="graphics-document document" viewBox="0 0 500 500" style="max-width: 500px; background-color: white;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><title id="chart-title-my-svg">Reach and Engagement of Roguelike Games</title><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:2px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g/><g class="main"><g class="quadrants"><g class="quadrant"><rect fill="#ECECFF" height="212" width="232" y="45" x="263"/><text transform="translate(379, 50) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#131300" y="0" x="0">Expand and improve</text></g><g class="quadrant"><rect fill="#f1f1ff" height="212" width="232" y="45" x="31"/><text transform="translate(147, 50) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#0e0e00" y="0" x="0">Promote and maintain</text></g><g class="quadrant"><rect fill="#f6f6ff" height="212" width="232" y="257" x="31"/><text transform="translate(147, 262) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#090900" y="0" x="0">Re-evaluate and iterate</text></g><g class="quadrant"><rect fill="#fbfbff" height="212" width="232" y="257" x="263"/><text transform="translate(379, 262) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#040400" y="0" x="0">Improve and innovate</text></g></g><g class="border"><line style="stroke: rgb(199, 199, 241); stroke-width: 2;" y2="45" x2="496" y1="45" x1="30"/><line style="stroke: rgb(199, 199, 241); stroke-width: 2;" y2="468" x2="495" y1="46" x1="495"/><line style="stroke: rgb(199, 199, 241); stroke-width: 2;" y2="469" x2="496" y1="469" x1="30"/><line style="stroke: rgb(199, 199, 241); stroke-width: 2;" y2="468" x2="31" y1="46" x1="31"/><line style="stroke: rgb(199, 199, 241); stroke-width: 1;" y2="468" x2="263" y1="46" x1="263"/><line style="stroke: rgb(199, 199, 241); stroke-width: 1;" y2="257" x2="494" y1="257" x1="32"/></g><g class="data-points"><g class="data-point"><circle fill="hsl(240, 100%, NaN%)" r="5" cy="172.20000000000002" cx="263"/><text transform="translate(263, 177.20000000000002) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#131300" y="0" x="0">Our Target Product</text></g><g class="data-point"><circle fill="hsl(240, 100%, NaN%)" r="5" cy="151" cx="170.2"/><text transform="translate(170.2, 156) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#131300" y="0" x="0">Dwarf Fortress Adventure Mode</text></g><g class="data-point"><circle fill="hsl(240, 100%, NaN%)" r="5" cy="214.60000000000002" cx="193.4"/><text transform="translate(193.4, 219.60000000000002) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#131300" y="0" x="0">ADOM</text></g><g class="data-point"><circle fill="hsl(240, 100%, NaN%)" r="5" cy="235.79999999999998" cx="263"/><text transform="translate(263, 240.79999999999998) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#131300" y="0" x="0">Tales of Maj'Eyal</text></g><g class="data-point"><circle fill="hsl(240, 100%, NaN%)" r="5" cy="193.39999999999998" cx="239.8"/><text transform="translate(239.8, 198.39999999999998) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#131300" y="0" x="0">Caves of Qud</text></g><g class="data-point"><circle fill="hsl(240, 100%, NaN%)" r="5" cy="257" cx="216.6"/><text transform="translate(216.6, 262) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#131300" y="0" x="0">Brogue</text></g><g class="data-point"><circle fill="hsl(240, 100%, NaN%)" r="5" cy="214.60000000000002" cx="286.2"/><text transform="translate(286.2, 219.60000000000002) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#131300" y="0" x="0">Dungeon Crawl Stone Soup</text></g><g class="data-point"><circle fill="hsl(240, 100%, NaN%)" r="5" cy="172.20000000000002" cx="309.4"/><text transform="translate(309.4, 177.20000000000002) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#131300" y="0" x="0">NetHack</text></g></g><g class="labels"><g class="label"><text transform="translate(31, 479) rotate(0)" text-anchor="start" dominant-baseline="hanging" font-size="16" fill="#131300" y="0" x="0">Low Reach</text></g><g class="label"><text transform="translate(263, 479) rotate(0)" text-anchor="start" dominant-baseline="hanging" font-size="16" fill="#131300" y="0" x="0">High Reach</text></g><g class="label"><text transform="translate(5, 469) rotate(-90)" text-anchor="start" dominant-baseline="hanging" font-size="16" fill="#131300" y="0" x="0">Low Engagement</text></g><g class="label"><text transform="translate(5, 257) rotate(-90)" text-anchor="start" dominant-baseline="hanging" font-size="16" fill="#131300" y="0" x="0">High Engagement</text></g></g><g class="title"><text transform="translate(250, 10) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="20" fill="#131300" y="0" x="0">Reach and Engagement of Roguelike Games</text></g></g></svg>