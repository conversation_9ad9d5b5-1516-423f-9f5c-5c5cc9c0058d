<svg aria-roledescription="classDiagram" role="graphics-document document" viewBox="0 0 1758.91015625 908.5" style="max-width: 1758.91px; background-color: white;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:2px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg g.classGroup text{fill:#9370DB;fill:#131300;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#my-svg g.classGroup text .title{font-weight:bolder;}#my-svg .nodeLabel,#my-svg .edgeLabel{color:#131300;}#my-svg .edgeLabel .label rect{fill:#ECECFF;}#my-svg .label text{fill:#131300;}#my-svg .edgeLabel .label span{background:#ECECFF;}#my-svg .classTitle{font-weight:bolder;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .divider{stroke:#9370DB;stroke-width:1;}#my-svg g.clickable{cursor:pointer;}#my-svg g.classGroup rect{fill:#ECECFF;stroke:#9370DB;}#my-svg g.classGroup line{stroke:#9370DB;stroke-width:1;}#my-svg .classLabel .box{stroke:none;stroke-width:0;fill:#ECECFF;opacity:0.5;}#my-svg .classLabel .label{fill:#9370DB;font-size:10px;}#my-svg .relation{stroke:#333333;stroke-width:1;fill:none;}#my-svg .dashed-line{stroke-dasharray:3;}#my-svg .dotted-line{stroke-dasharray:1 2;}#my-svg #compositionStart,#my-svg .composition{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #compositionEnd,#my-svg .composition{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #extensionStart,#my-svg .extension{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #extensionEnd,#my-svg .extension{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #aggregationStart,#my-svg .aggregation{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #aggregationEnd,#my-svg .aggregation{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #lollipopStart,#my-svg .lollipop{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #lollipopEnd,#my-svg .lollipop{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg .edgeTerminals{font-size:11px;}#my-svg .classTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker aggregation classDiagram" id="classDiagram-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker aggregation classDiagram" id="classDiagram-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker extension classDiagram" id="classDiagram-extensionStart"><path d="M 1,7 L18,13 V 1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker extension classDiagram" id="classDiagram-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker composition classDiagram" id="classDiagram-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker composition classDiagram" id="classDiagram-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker dependency classDiagram" id="classDiagram-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker dependency classDiagram" id="classDiagram-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker lollipop classDiagram" id="classDiagram-lollipopStart"><circle r="6" cy="7" cx="6" fill="white" stroke="black"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path style="fill:none" class="edge-pattern-solid relation" id="id1" d="M1069.82421875,172.87458475759473L1084.6380208333333,177.8954872979956C1099.4518229166667,182.9163898383965,1129.0794270833333,192.95819491919823,1143.8932291666667,202.1457641262658C1158.70703125,211.33333333333334,1158.70703125,219.66666666666666,1158.70703125,223.83333333333334L1158.70703125,228"/><path style="fill:none" class="edge-pattern-solid relation" id="id2" d="M947.2288707386364,178L952.771454782197,182.16666666666666C958.3140388257576,186.33333333333334,969.3992069128789,194.66666666666666,974.9417909564394,217.16666666666666C980.484375,239.66666666666666,980.484375,276.3333333333333,980.484375,313C980.484375,349.6666666666667,980.484375,386.3333333333333,980.484375,408.8333333333333C980.484375,431.3333333333333,980.484375,439.6666666666667,980.484375,443.8333333333333L980.484375,448"/><path style="fill:none" class="edge-pattern-solid relation" id="id3" d="M598.49609375,133.04549870564028L529.8834635416666,144.70458225470023C461.2708333333333,156.3636658037602,324.0455729166667,179.68183290188009,255.43294270833334,209.67424978427337C186.8203125,239.66666666666666,186.8203125,276.3333333333333,186.8203125,313C186.8203125,349.6666666666667,186.8203125,386.3333333333333,186.8203125,419.25C186.8203125,452.1666666666667,186.8203125,481.3333333333333,186.8203125,510.5C186.8203125,539.6666666666666,186.8203125,568.8333333333334,186.8203125,587.5833333333334C186.8203125,606.3333333333334,186.8203125,614.6666666666666,186.8203125,618.8333333333334L186.8203125,623"/><path style="fill:none" class="edge-pattern-solid relation" id="id4" d="M598.49609375,160.01370305668036L573.3014322916666,167.17808588056695C548.1067708333334,174.34246870445358,497.7174479166667,188.67123435222678,472.5227864583333,203.75228384278003C447.328125,218.83333333333334,447.328125,234.66666666666666,447.328125,242.58333333333334L447.328125,250.5"/><path style="fill:none" class="edge-pattern-solid relation" id="id5" d="M834.16015625,178L834.16015625,182.16666666666666C834.16015625,186.33333333333334,834.16015625,194.66666666666666,834.16015625,206.75C834.16015625,218.83333333333334,834.16015625,234.66666666666666,834.16015625,242.58333333333334L834.16015625,250.5"/><path style="fill:none" class="edge-pattern-solid relation" id="id6" d="M1158.70703125,398L1158.70703125,402.1666666666667C1158.70703125,406.3333333333333,1158.70703125,414.6666666666667,1150.220238095238,423C1141.7334449404761,431.3333333333333,1124.7598586309525,439.6666666666667,1116.2730654761906,443.8333333333333L1107.7862723214287,448"/><path marker-start="url(#classDiagram-extensionStart)" style="fill:none" class="edge-pattern-solid relation" id="id7" d="M743.62890625,562.7257577099883L716.9661458333334,568.6047980916569C690.3033854166666,574.4838384733256,636.9778645833334,586.2419192366627,573.9798177083334,600.1327563351675C510.9817708333333,614.0235934336723,438.3111979166667,630.0471868673445,401.9759114583333,638.0589835841807L365.640625,646.0707803010168"/><path marker-start="url(#classDiagram-extensionStart)" style="fill:none" class="edge-pattern-solid relation" id="id8" d="M823.6275111607142,573L813.1703869047618,577.1666666666666C802.7132626488095,581.3333333333334,781.7990141369047,589.6666666666666,771.3418898809523,599.875C760.884765625,610.0833333333334,760.884765625,622.1666666666666,760.884765625,628.2083333333334L760.884765625,634.25"/><path marker-start="url(#classDiagram-extensionStart)" style="fill:none" class="edge-pattern-solid relation" id="id9" d="M1142.9132254464287,573L1153.7418154761906,577.1666666666666C1164.5704055059525,581.3333333333334,1186.2275855654761,589.6666666666666,1197.056175595238,599.875C1207.884765625,610.0833333333334,1207.884765625,622.1666666666666,1207.884765625,628.2083333333334L1207.884765625,634.25"/><path marker-start="url(#classDiagram-extensionStart)" style="fill:none" class="edge-pattern-solid relation" id="id10" d="M1217.33984375,550.4117033080698L1264.4085286458333,558.3430860900581C1311.4772135416667,566.2744688720466,1405.6145833333333,582.1372344360233,1452.6832682291667,596.1102838846783C1499.751953125,610.0833333333334,1499.751953125,622.1666666666666,1499.751953125,628.2083333333334L1499.751953125,634.25"/><path style="fill:none" class="edge-pattern-solid relation" id="id11" d="M186.8203125,748L186.8203125,752.1666666666666C186.8203125,756.3333333333334,186.8203125,764.6666666666666,186.8203125,773C186.8203125,781.3333333333334,186.8203125,789.6666666666666,186.8203125,793.8333333333334L186.8203125,798"/><path style="fill:none" class="edge-pattern-solid relation" id="id12" d="M760.884765625,736.75L760.884765625,742.7916666666666C760.884765625,748.8333333333334,760.884765625,760.9166666666666,815.9918619791666,778.3800389150505C871.0989583333334,795.8434111634343,981.3131510416666,818.6868223268688,1036.4202473958333,830.1085279085859L1091.52734375,841.5302334903031"/><path style="fill:none" class="edge-pattern-solid relation" id="id13" d="M1207.884765625,736.75L1207.884765625,742.7916666666666C1207.884765625,748.8333333333334,1207.884765625,760.9166666666666,1199.6710211748634,774.875C1191.4572767247266,788.8333333333334,1175.0297878244535,804.6666666666666,1166.816043374317,812.5833333333334L1158.6022989241803,820.5"/><path style="fill:none" class="edge-pattern-solid relation" id="id14" d="M1499.751953125,736.75L1499.751953125,742.7916666666666C1499.751953125,748.8333333333334,1499.751953125,760.9166666666666,1444.1298828125,778.390755111675C1388.5078125,795.8648435566834,1277.263671875,818.7296871133667,1221.6416015625,830.1621088917085L1166.01953125,841.5945306700502"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(834.16015625, 93)" id="classId-Game-0" class="node default"><rect height="170" width="471.328125" y="-85" x="-235.6640625" class="outer title-state"/><line y2="-54.5" y1="-54.5" x2="235.6640625" x1="-235.6640625" class="divider"/><line y2="-38.5" y1="-38.5" x2="235.6640625" x1="-235.6640625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -21.10546875, -77.5)" height="18.5" width="42.2109375" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Game</span></div></foreignObject><foreignObject transform="translate( -228.1640625, -31)" height="18.5" width="456.328125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, player: Player, level: Level, ui: UI, audio: Audio)</span></div></foreignObject><foreignObject transform="translate( -228.1640625, -8.5)" height="18.5" width="69.6796875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+run(self)</span></div></foreignObject><foreignObject transform="translate( -228.1640625, 14)" height="18.5" width="248.96875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+handle_input(self, key: tcod.Key)</span></div></foreignObject><foreignObject transform="translate( -228.1640625, 36.5)" height="18.5" width="96.0234375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+update(self)</span></div></foreignObject><foreignObject transform="translate( -228.1640625, 59)" height="18.5" width="93.5234375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+render(self)</span></div></foreignObject></g></g><g transform="translate(1158.70703125, 313)" id="classId-Level-1" class="node default"><rect height="170" width="286.4453125" y="-85" x="-143.22265625" class="outer title-state"/><line y2="-54.5" y1="-54.5" x2="143.22265625" x1="-143.22265625" class="divider"/><line y2="-38.5" y1="-38.5" x2="143.22265625" x1="-143.22265625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -20.1953125, -77.5)" height="18.5" width="40.390625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Level</span></div></foreignObject><foreignObject transform="translate( -135.72265625, -31)" height="18.5" width="271.4453125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, width: int, height: int)</span></div></foreignObject><foreignObject transform="translate( -135.72265625, -8.5)" height="18.5" width="219.21875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+generate(self, algorithm: str)</span></div></foreignObject><foreignObject transform="translate( -135.72265625, 14)" height="18.5" width="260.75"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+get_tile(self, x: int, y: int) : -&gt; Tile</span></div></foreignObject><foreignObject transform="translate( -135.72265625, 36.5)" height="18.5" width="247.875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+spawn_entity(self, entity: Entity)</span></div></foreignObject><foreignObject transform="translate( -135.72265625, 59)" height="18.5" width="256.8046875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+remove_entity(self, entity: Entity)</span></div></foreignObject></g></g><g transform="translate(980.484375, 510.5)" id="classId-Entity-2" class="node default"><rect height="125" width="473.7109375" y="-62.5" x="-236.85546875" class="outer title-state"/><line y2="-32" y1="-32" x2="236.85546875" x1="-236.85546875" class="divider"/><line y2="-16" y1="-16" x2="236.85546875" x1="-236.85546875" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -22.2734375, -55)" height="18.5" width="44.546875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Entity</span></div></foreignObject><foreignObject transform="translate( -229.35546875, -8.5)" height="18.5" width="458.7109375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, x: int, y: int, char: str, color: Tuple[int, int, int])</span></div></foreignObject><foreignObject transform="translate( -229.35546875, 14)" height="18.5" width="200.21875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+move(self, dx: int, dy: int)</span></div></foreignObject><foreignObject transform="translate( -229.35546875, 36.5)" height="18.5" width="205.703125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+interact(self, other: Entity)</span></div></foreignObject></g></g><g transform="translate(186.8203125, 685.5)" id="classId-Player-3" class="node default"><rect height="125" width="357.640625" y="-62.5" x="-178.8203125" class="outer title-state"/><line y2="-32" y1="-32" x2="178.8203125" x1="-178.8203125" class="divider"/><line y2="-16" y1="-16" x2="178.8203125" x1="-178.8203125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -23.6015625, -55)" height="18.5" width="47.203125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Player</span></div></foreignObject><foreignObject transform="translate( -171.3203125, -8.5)" height="18.5" width="342.640625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, character_class: CharacterClass)</span></div></foreignObject><foreignObject transform="translate( -171.3203125, 14)" height="18.5" width="258.53125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+gain_experience(self, amount: int)</span></div></foreignObject><foreignObject transform="translate( -171.3203125, 36.5)" height="18.5" width="106.75"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+level_up(self)</span></div></foreignObject></g></g><g transform="translate(186.8203125, 849.25)" id="classId-CharacterClass-4" class="node default"><rect height="102.5" width="330.9921875" y="-51.25" x="-165.49609375" class="outer title-state"/><line y2="-20.75" y1="-20.75" x2="165.49609375" x1="-165.49609375" class="divider"/><line y2="-4.75" y1="-4.75" x2="165.49609375" x1="-165.49609375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -55.015625, -43.75)" height="18.5" width="110.03125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">CharacterClass</span></div></foreignObject><foreignObject transform="translate( -157.99609375, 2.75)" height="18.5" width="315.9921875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, name: str, abilities: List[str])</span></div></foreignObject><foreignObject transform="translate( -157.99609375, 25.25)" height="18.5" width="315.171875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+use_ability(self, ability: str, target: Entity)</span></div></foreignObject></g></g><g transform="translate(760.884765625, 685.5)" id="classId-Enemy-5" class="node default"><rect height="102.5" width="211.2890625" y="-51.25" x="-105.64453125" class="outer title-state"/><line y2="-20.75" y1="-20.75" x2="105.64453125" x1="-105.64453125" class="divider"/><line y2="-4.75" y1="-4.75" x2="105.64453125" x1="-105.64453125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -25.015625, -43.75)" height="18.5" width="50.03125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Enemy</span></div></foreignObject><foreignObject transform="translate( -98.14453125, 2.75)" height="18.5" width="196.2890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, ai: Callable)</span></div></foreignObject><foreignObject transform="translate( -98.14453125, 25.25)" height="18.5" width="115.9609375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+take_turn(self)</span></div></foreignObject></g></g><g transform="translate(1207.884765625, 685.5)" id="classId-Item-6" class="node default"><rect height="102.5" width="241.8671875" y="-51.25" x="-120.93359375" class="outer title-state"/><line y2="-20.75" y1="-20.75" x2="120.93359375" x1="-120.93359375" class="divider"/><line y2="-4.75" y1="-4.75" x2="120.93359375" x1="-120.93359375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -16.87109375, -43.75)" height="18.5" width="33.7421875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Item</span></div></foreignObject><foreignObject transform="translate( -113.43359375, 2.75)" height="18.5" width="226.8671875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, effect: Callable)</span></div></foreignObject><foreignObject transform="translate( -113.43359375, 25.25)" height="18.5" width="177.8359375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+use(self, target: Entity)</span></div></foreignObject></g></g><g transform="translate(1499.751953125, 685.5)" id="classId-Trap-7" class="node default"><rect height="102.5" width="241.8671875" y="-51.25" x="-120.93359375" class="outer title-state"/><line y2="-20.75" y1="-20.75" x2="120.93359375" x1="-120.93359375" class="divider"/><line y2="-4.75" y1="-4.75" x2="120.93359375" x1="-120.93359375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -16.1640625, -43.75)" height="18.5" width="32.328125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Trap</span></div></foreignObject><foreignObject transform="translate( -113.43359375, 2.75)" height="18.5" width="226.8671875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, effect: Callable)</span></div></foreignObject><foreignObject transform="translate( -113.43359375, 25.25)" height="18.5" width="202.0234375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+trigger(self, target: Entity)</span></div></foreignObject></g></g><g transform="translate(447.328125, 313)" id="classId-UI-8" class="node default"><rect height="125" width="451.015625" y="-62.5" x="-225.5078125" class="outer title-state"/><line y2="-32" y1="-32" x2="225.5078125" x1="-225.5078125" class="divider"/><line y2="-16" y1="-16" x2="225.5078125" x1="-225.5078125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -7.6484375, -55)" height="18.5" width="15.296875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">UI</span></div></foreignObject><foreignObject transform="translate( -218.0078125, -8.5)" height="18.5" width="271.4453125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self, width: int, height: int)</span></div></foreignObject><foreignObject transform="translate( -218.0078125, 14)" height="18.5" width="182.4921875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+draw(self, game: Game)</span></div></foreignObject><foreignObject transform="translate( -218.0078125, 36.5)" height="18.5" width="436.015625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+show_message(self, message: str, color: Tuple[int, int, int])</span></div></foreignObject></g></g><g transform="translate(834.16015625, 313)" id="classId-Audio-9" class="node default"><rect height="125" width="222.6484375" y="-62.5" x="-111.32421875" class="outer title-state"/><line y2="-32" y1="-32" x2="111.32421875" x1="-111.32421875" class="divider"/><line y2="-16" y1="-16" x2="111.32421875" x1="-111.32421875" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -21.3515625, -55)" height="18.5" width="42.703125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Audio</span></div></foreignObject><foreignObject transform="translate( -103.82421875, -8.5)" height="18.5" width="103.75"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self)</span></div></foreignObject><foreignObject transform="translate( -103.82421875, 14)" height="18.5" width="207.6484375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+play_sound(self, sound: str)</span></div></foreignObject><foreignObject transform="translate( -103.82421875, 36.5)" height="18.5" width="206.6953125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+play_music(self, music: str)</span></div></foreignObject></g></g><g transform="translate(1291.7421875, 93)" id="classId-SaveLoad-10" class="node default"><rect height="125" width="343.8359375" y="-62.5" x="-171.91796875" class="outer title-state"/><line y2="-32" y1="-32" x2="171.91796875" x1="-171.91796875" class="divider"/><line y2="-16" y1="-16" x2="171.91796875" x1="-171.91796875" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -35.0234375, -55)" height="18.5" width="70.046875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">SaveLoad</span></div></foreignObject><foreignObject transform="translate( -164.41796875, -8.5)" height="18.5" width="103.75"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self)</span></div></foreignObject><foreignObject transform="translate( -164.41796875, 14)" height="18.5" width="328.8359375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+save_game(self, game: Game, filename: str)</span></div></foreignObject><foreignObject transform="translate( -164.41796875, 36.5)" height="18.5" width="302.78125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+load_game(self, filename: str) : -&gt; Game</span></div></foreignObject></g></g><g transform="translate(1632.28515625, 93)" id="classId-Tutorial-11" class="node default"><rect height="102.5" width="237.25" y="-51.25" x="-118.625" class="outer title-state"/><line y2="-20.75" y1="-20.75" x2="118.625" x1="-118.625" class="divider"/><line y2="-4.75" y1="-4.75" x2="118.625" x1="-118.625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -29.0234375, -43.75)" height="18.5" width="58.046875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Tutorial</span></div></foreignObject><foreignObject transform="translate( -111.125, 2.75)" height="18.5" width="103.75"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(self)</span></div></foreignObject><foreignObject transform="translate( -111.125, 25.25)" height="18.5" width="222.25"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+show_help(self, game: Game)</span></div></foreignObject></g></g><g transform="translate(1128.7734375, 849.25)" id="classId-Callable-12" class="node default"><rect height="57.5" width="74.4921875" y="-28.75" x="-37.24609375" class="outer title-state"/><line y2="1.75" y1="1.75" x2="37.24609375" x1="-37.24609375" class="divider"/><line y2="17.75" y1="17.75" x2="37.24609375" x1="-37.24609375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -29.74609375, -21.25)" height="18.5" width="59.4921875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Callable</span></div></foreignObject></g></g></g></g></g></svg>