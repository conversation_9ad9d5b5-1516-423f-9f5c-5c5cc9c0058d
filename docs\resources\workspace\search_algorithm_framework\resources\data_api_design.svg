<svg aria-roledescription="classDiagram" role="graphics-document document" viewBox="0 0 3446.15625 338.5" style="max-width: 3446.16px; background-color: white;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:2px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg g.classGroup text{fill:#9370DB;fill:#131300;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#my-svg g.classGroup text .title{font-weight:bolder;}#my-svg .nodeLabel,#my-svg .edgeLabel{color:#131300;}#my-svg .edgeLabel .label rect{fill:#ECECFF;}#my-svg .label text{fill:#131300;}#my-svg .edgeLabel .label span{background:#ECECFF;}#my-svg .classTitle{font-weight:bolder;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .divider{stroke:#9370DB;stroke-width:1;}#my-svg g.clickable{cursor:pointer;}#my-svg g.classGroup rect{fill:#ECECFF;stroke:#9370DB;}#my-svg g.classGroup line{stroke:#9370DB;stroke-width:1;}#my-svg .classLabel .box{stroke:none;stroke-width:0;fill:#ECECFF;opacity:0.5;}#my-svg .classLabel .label{fill:#9370DB;font-size:10px;}#my-svg .relation{stroke:#333333;stroke-width:1;fill:none;}#my-svg .dashed-line{stroke-dasharray:3;}#my-svg .dotted-line{stroke-dasharray:1 2;}#my-svg #compositionStart,#my-svg .composition{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #compositionEnd,#my-svg .composition{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#333333!important;stroke:#333333!important;stroke-width:1;}#my-svg #extensionStart,#my-svg .extension{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #extensionEnd,#my-svg .extension{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #aggregationStart,#my-svg .aggregation{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #aggregationEnd,#my-svg .aggregation{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #lollipopStart,#my-svg .lollipop{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg #lollipopEnd,#my-svg .lollipop{fill:#ECECFF!important;stroke:#333333!important;stroke-width:1;}#my-svg .edgeTerminals{font-size:11px;}#my-svg .classTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker aggregation classDiagram" id="classDiagram-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker aggregation classDiagram" id="classDiagram-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker extension classDiagram" id="classDiagram-extensionStart"><path d="M 1,7 L18,13 V 1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker extension classDiagram" id="classDiagram-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker composition classDiagram" id="classDiagram-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker composition classDiagram" id="classDiagram-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker dependency classDiagram" id="classDiagram-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" class="marker dependency classDiagram" id="classDiagram-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" class="marker lollipop classDiagram" id="classDiagram-lollipopStart"><circle r="6" cy="7" cx="6" fill="white" stroke="black"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path style="fill:none" class="edge-pattern-solid relation" id="id1" d="M1582.3984375,93.48360130131726L1344.869140625,107.9863344177644C1107.33984375,122.48906753421151,632.28125,151.49453376710576,394.751953125,173.91393355021955C157.22265625,196.33333333333334,157.22265625,212.16666666666666,157.22265625,220.08333333333334L157.22265625,228"/><path style="fill:none" class="edge-pattern-solid relation" id="id2" d="M1582.3984375,97.62186651594797L1415.1510416666667,111.43488876328998C1247.9036458333333,125.24791101063198,913.4088541666666,152.873955505316,746.1614583333334,174.6036444193247C578.9140625,196.33333333333334,578.9140625,212.16666666666666,578.9140625,220.08333333333334L578.9140625,228"/><path style="fill:none" class="edge-pattern-solid relation" id="id3" d="M1582.3984375,114.31889090750636L1517.3138020833333,125.34907575625529C1452.2291666666667,136.37926060500425,1322.0598958333333,158.4396303025021,1256.9752604166667,177.3864818179177C1191.890625,196.33333333333334,1191.890625,212.16666666666666,1191.890625,220.08333333333334L1191.890625,228"/><path style="fill:none" class="edge-pattern-solid relation" id="id4" d="M1774.57421875,155.5L1774.57421875,159.66666666666666C1774.57421875,163.83333333333334,1774.57421875,172.16666666666666,1774.57421875,180.5C1774.57421875,188.83333333333334,1774.57421875,197.16666666666666,1774.57421875,201.33333333333334L1774.57421875,205.5"/><path style="fill:none" class="edge-pattern-solid relation" id="id5" d="M1966.75,126.32026770396601L2005.6848958333333,135.35022308663835C2044.6197916666667,144.38017846931066,2122.4895833333335,162.44008923465535,2161.4244791666665,179.38671128399434C2200.359375,196.33333333333334,2200.359375,212.16666666666666,2200.359375,220.08333333333334L2200.359375,228"/><path style="fill:none" class="edge-pattern-solid relation" id="id6" d="M1966.75,101.33858341531861L2096.1868489583335,114.53215284609884C2225.6236979166665,127.72572227687908,2484.4973958333335,154.11286113843954,2613.9342447916665,173.34809723588646C2743.37109375,192.58333333333334,2743.37109375,204.66666666666666,2743.37109375,210.70833333333334L2743.37109375,216.75"/><path style="fill:none" class="edge-pattern-solid relation" id="id7" d="M1966.75,94.77942560826467L2177.470703125,109.06618800688723C2388.19140625,123.35295040550977,2809.6328125,151.9264752027549,3020.353515625,174.12990426804413C3231.07421875,196.33333333333334,3231.07421875,212.16666666666666,3231.07421875,220.08333333333334L3231.07421875,228"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1774.57421875, 81.75)" id="classId-SearchAPI-0" class="node default"><rect height="147.5" width="384.3515625" y="-73.75" x="-192.17578125" class="outer title-state"/><line y2="-43.25" y1="-43.25" x2="192.17578125" x1="-192.17578125" class="divider"/><line y2="-27.25" y1="-27.25" x2="192.17578125" x1="-192.17578125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -37.19140625, -66.25)" height="18.5" width="74.3828125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">SearchAPI</span></div></foreignObject><foreignObject transform="translate( -184.67578125, -19.75)" height="18.5" width="300.078125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+search(query: str) : -&gt; List[SearchResult]</span></div></foreignObject><foreignObject transform="translate( -184.67578125, 2.75)" height="18.5" width="369.3515625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+update_document(document: Document) : -&gt; bool</span></div></foreignObject><foreignObject transform="translate( -184.67578125, 25.25)" height="18.5" width="334.2109375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+delete_document(document_id: str) : -&gt; bool</span></div></foreignObject><foreignObject transform="translate( -184.67578125, 47.75)" height="18.5" width="345.5390625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+add_document(document: Document) : -&gt; bool</span></div></foreignObject></g></g><g transform="translate(2256.8125, 81.75)" id="classId-Document-1" class="node default"><rect height="80" width="480.125" y="-40" x="-240.0625" class="outer title-state"/><line y2="-9.5" y1="-9.5" x2="240.0625" x1="-240.0625" class="divider"/><line y2="6.5" y1="6.5" x2="240.0625" x1="-240.0625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -37.85546875, -32.5)" height="18.5" width="75.7109375" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Document</span></div></foreignObject><foreignObject transform="translate( -232.5625, 14)" height="18.5" width="465.125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(document_id: str, title: str, content: str, language: str)</span></div></foreignObject></g></g><g transform="translate(2734.6875, 81.75)" id="classId-SearchResult-2" class="node default"><rect height="80" width="375.625" y="-40" x="-187.8125" class="outer title-state"/><line y2="-9.5" y1="-9.5" x2="187.8125" x1="-187.8125" class="divider"/><line y2="6.5" y1="6.5" x2="187.8125" x1="-187.8125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -48.390625, -32.5)" height="18.5" width="96.78125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">SearchResult</span></div></foreignObject><foreignObject transform="translate( -180.3125, 14)" height="18.5" width="360.625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(document_id: str, title: str, score: float)</span></div></foreignObject></g></g><g transform="translate(157.22265625, 268)" id="classId-QueryUnderstanding-3" class="node default"><rect height="80" width="298.4453125" y="-40" x="-149.22265625" class="outer title-state"/><line y2="-9.5" y1="-9.5" x2="149.22265625" x1="-149.22265625" class="divider"/><line y2="6.5" y1="6.5" x2="149.22265625" x1="-149.22265625" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -76.859375, -32.5)" height="18.5" width="153.71875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">QueryUnderstanding</span></div></foreignObject><foreignObject transform="translate( -141.72265625, 14)" height="18.5" width="283.4453125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+process_query(query: str) : -&gt; List[str]</span></div></foreignObject></g></g><g transform="translate(578.9140625, 268)" id="classId-Recall-4" class="node default"><rect height="80" width="444.9375" y="-40" x="-222.46875" class="outer title-state"/><line y2="-9.5" y1="-9.5" x2="222.46875" x1="-222.46875" class="divider"/><line y2="6.5" y1="6.5" x2="222.46875" x1="-222.46875" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -22.55859375, -32.5)" height="18.5" width="45.1171875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Recall</span></div></foreignObject><foreignObject transform="translate( -214.96875, 14)" height="18.5" width="429.9375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+get_candidates(query_terms: List[str]) : -&gt; List[Document]</span></div></foreignObject></g></g><g transform="translate(1191.890625, 268)" id="classId-Ranking-5" class="node default"><rect height="80" width="681.015625" y="-40" x="-340.5078125" class="outer title-state"/><line y2="-9.5" y1="-9.5" x2="340.5078125" x1="-340.5078125" class="divider"/><line y2="6.5" y1="6.5" x2="340.5078125" x1="-340.5078125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -29.37890625, -32.5)" height="18.5" width="58.7578125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Ranking</span></div></foreignObject><foreignObject transform="translate( -333.0078125, 14)" height="18.5" width="666.015625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+rank_documents(query_terms: List[str], candidates: List[Document]) : -&gt; List[SearchResult]</span></div></foreignObject></g></g><g transform="translate(1774.57421875, 268)" id="classId-Indexing-6" class="node default"><rect height="125" width="384.3515625" y="-62.5" x="-192.17578125" class="outer title-state"/><line y2="-32" y1="-32" x2="192.17578125" x1="-192.17578125" class="divider"/><line y2="-16" y1="-16" x2="192.17578125" x1="-192.17578125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -31.734375, -55)" height="18.5" width="63.46875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Indexing</span></div></foreignObject><foreignObject transform="translate( -184.67578125, -8.5)" height="18.5" width="358.265625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+index_document(document: Document) : -&gt; bool</span></div></foreignObject><foreignObject transform="translate( -184.67578125, 14)" height="18.5" width="369.3515625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+update_document(document: Document) : -&gt; bool</span></div></foreignObject><foreignObject transform="translate( -184.67578125, 36.5)" height="18.5" width="334.2109375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+delete_document(document_id: str) : -&gt; bool</span></div></foreignObject></g></g><g transform="translate(2200.359375, 268)" id="classId-UserFeedback-7" class="node default"><rect height="80" width="367.21875" y="-40" x="-183.609375" class="outer title-state"/><line y2="-9.5" y1="-9.5" x2="183.609375" x1="-183.609375" class="divider"/><line y2="6.5" y1="6.5" x2="183.609375" x1="-183.609375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -52.40625, -32.5)" height="18.5" width="104.8125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">UserFeedback</span></div></foreignObject><foreignObject transform="translate( -176.109375, 14)" height="18.5" width="352.21875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+submit_feedback(feedback: Feedback) : -&gt; bool</span></div></foreignObject></g></g><g transform="translate(3205.328125, 81.75)" id="classId-Feedback-8" class="node default"><rect height="80" width="465.65625" y="-40" x="-232.828125" class="outer title-state"/><line y2="-9.5" y1="-9.5" x2="232.828125" x1="-232.828125" class="divider"/><line y2="6.5" y1="6.5" x2="232.828125" x1="-232.828125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -35.5234375, -32.5)" height="18.5" width="71.046875" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Feedback</span></div></foreignObject><foreignObject transform="translate( -225.328125, 14)" height="18.5" width="450.65625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+__init__(user_id: str, query: str, document_id: str, rating: int)</span></div></foreignObject></g></g><g transform="translate(2743.37109375, 268)" id="classId-MachineLearning-9" class="node default"><rect height="102.5" width="618.8046875" y="-51.25" x="-309.40234375" class="outer title-state"/><line y2="-20.75" y1="-20.75" x2="309.40234375" x1="-309.40234375" class="divider"/><line y2="-4.75" y1="-4.75" x2="309.40234375" x1="-309.40234375" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -63.3125, -43.75)" height="18.5" width="126.625" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">MachineLearning</span></div></foreignObject><foreignObject transform="translate( -301.90234375, 2.75)" height="18.5" width="394.5"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+train_model(feedback_data: List[Feedback]) : -&gt; bool</span></div></foreignObject><foreignObject transform="translate( -301.90234375, 25.25)" height="18.5" width="603.8046875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+predict_ranking(query_terms: List[str], candidates: List[Document]) : -&gt; List[float]</span></div></foreignObject></g></g><g transform="translate(3231.07421875, 268)" id="classId-Utils-10" class="node default"><rect height="80" width="256.6015625" y="-40" x="-128.30078125" class="outer title-state"/><line y2="-9.5" y1="-9.5" x2="128.30078125" x1="-128.30078125" class="divider"/><line y2="6.5" y1="6.5" x2="128.30078125" x1="-128.30078125" class="divider"/><g class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject><foreignObject transform="translate( -16.78515625, -32.5)" height="18.5" width="33.5703125" class="classTitle"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Utils</span></div></foreignObject><foreignObject transform="translate( -120.80078125, 14)" height="18.5" width="241.6015625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">+normalize_text(text: str) : -&gt; str</span></div></foreignObject></g></g></g></g></g></svg>