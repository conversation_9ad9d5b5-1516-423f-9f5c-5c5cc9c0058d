Productivity
I think I am at least somewhat more productive than average, and people sometimes ask me for productivity tips.  So I decided to just write them all down in one place.

Compound growth gets discussed as a financial concept, but it works in careers as well, and it is magic.  A small productivity gain, compounded over 50 years, is worth a lot.  So it’s worth figuring out how to optimize productivity. If you get 10% more done and 1% better every day compared to someone else, the compounded difference is massive. 

What you work on

Famous writers have some essential qualities, creativity and discipline

It doesn’t matter how fast you move if it’s in a worthless direction.  Picking the right thing to work on is the most important element of productivity and usually almost ignored.  So think about it more!  Independent thought is hard but it’s something you can get better at with practice.

The most impressive people I know have strong beliefs about the world, which is rare in the general population.  If you find yourself always agreeing with whomever you last spoke with, that’s bad.  You will of course be wrong sometimes, but develop the confidence to stick with your convictions.  It will let you be courageous when you’re right about something important that most people don’t see.

I make sure to leave enough time in my schedule to think about what to work on.  The best ways for me to do this are reading books, hanging out with interesting people, and spending time in nature.

I’ve learned that I can’t be very productive working on things I don’t care about or don’t like.  So I just try not to put myself in a position where I have to do them (by delegating, avoiding, or something else).  Stuff that you don’t like is a painful drag on morale and momentum.

By the way, here is an important lesson about delegation: remember that everyone else is also most productive when they’re doing what they like, and do what you’d want other people to do for you—try to figure out who likes (and is good at) doing what, and delegate that way.  

If you find yourself not liking what you’re doing for a long period of time, seriously consider a major job change.  Short-term burnout happens, but if it isn’t resolved with some time off, maybe it’s time to do something you’re more interested in. 

I’ve been very fortunate to find work I like so much I’d do it for free, which makes it easy to be really productive.

It’s important to learn that you can learn anything you want, and that you can get better quickly.  This feels like an unlikely miracle the first few times it happens, but eventually you learn to trust that you can do it.

Doing great work usually requires colleagues of some sort.  Try to be around smart, productive, happy, and positive people that don’t belittle your ambitions.  I love being around people who push me and inspire me to be better.  To the degree you able to, avoid the opposite kind of people—the cost of letting them take up your mental cycles is horrific. 

You have to both pick the right problem and do the work.  There aren’t many shortcuts.  If you’re going to do something really important, you are very likely going to work both smart and hard.  The biggest prizes are heavily competed for.  This isn’t true in every field (there are great mathematicians who never spend that many hours a week working) but it is in most.

﻿Prioritization

Writers have to work hard to be successful 

My system has three key pillars: “Make sure to get the important shit done”, “Don’t waste time on stupid shit”, and “make a lot of lists”.

I highly recommend using lists.  I make lists of what I want to accomplish each year, each month, and each day.  Lists are very focusing, and they help me with multitasking because I don’t have to keep as much in my head.  If I’m not in the mood for some particular task, I can always find something else I’m excited to do.

I prefer lists written down on paper.  It’s easy to add and remove tasks.  I can access them during meetings without feeling rude.  I re-transcribe lists frequently, which forces me to think about everything on the list and gives me an opportunity to add and remove items.

I don’t bother with categorization or trying to size tasks or anything like that (the most I do is put a star next to really important items).  

I try to prioritize in a way that generates momentum.  The more I get done, the better I feel, and then the more I get done.  I like to start and end each day with something I can really make progress on.

I am relentless about getting my most important projects done—I’ve found that if I really want something to happen and I push hard enough, it usually happens. 

I try to be ruthless about saying no to stuff, and doing non-critical things in the quickest way possible.  I probably take this too far—for example, I am almost sure I am terse to the point of rudeness when replying to emails.

Passion and adaptability are key qualities to writers

I generally try to avoid meetings and conferences as I find the time cost to be huge—I get the most value out of time in my office.  However, it is critical that you keep enough space in your schedule to allow for chance encounters and exposure to new people and ideas.  Having an open network is valuable; though probably 90% of the random meetings I take are a waste of time, the other 10% really make up for it.

I find most meetings are best scheduled for 15-20 minutes, or 2 hours.  The default of 1 hour is usually wrong, and leads to a lot of wasted time.

I have different times of day I try to use for different kinds of work.  The first few hours of the morning are definitely my most productive time of the day, so I don’t let anyone schedule anything then.  I try to do meetings in the afternoon.  I take a break, or switch tasks, whenever I feel my attention starting to fade. 

I don’t think most people value their time enough—I am surprised by the number of people I know who make $100 an hour and yet will spend a couple of hours doing something they don’t want to do to save $20.

Also, don’t fall into the trap of productivity porn—chasing productivity for its own sake isn’t helpful.  Many people spend too much time thinking about how to perfectly optimize their system, and not nearly enough asking if they’re working on the right problems.  It doesn’t matter what system you use or if you squeeze out every second if you’re working on the wrong thing.

The right goal is to allocate your year optimally, not your day.

Physical factors

Very likely what is optimal for me won’t be optimal for you.  You’ll have to experiment to find out what works best for your body.  It’s definitely worth doing—it helps in all aspects of life, and you’ll feel a lot better and happier overall.

It probably took a little bit of my time every week for a few years to arrive at what works best for me, but my sense is if I do a good job at all the below I’m at least 1.5x more productive than if not.

Sleep seems to be the most important physical factor in productivity for me.  Some sort of sleep tracker to figure out how to sleep best is helpful.  I’ve found the only thing I’m consistent with are in the set-it-and-forget-it category, and I really like the Emfit QS+Active.

I like a cold, dark, quiet room, and a great mattress (I resisted spending a bunch of money on a great mattress for years, which was stupid—it makes a huge difference to my sleep quality.  I love this one).  Not eating a lot in the few hours before sleep helps.  Not drinking alcohol helps a lot, though I’m not willing to do that all the time.

I use a Chili Pad to be cold while I sleep if I can’t get the room cold enough, which is great but loud (I set it up to have the cooler unit outside my room).

When traveling, I use an eye mask and ear plugs.

Writers usually have empathy to write good books.

This is likely to be controversial, but I take a low dose of sleeping pills (like a third of a normal dose) or a very low dose of cannabis whenever I can’t sleep.  I am a bad sleeper in general, and a particularly bad sleeper when I travel.  It likely has tradeoffs, but so does not sleeping well.  If you can already sleep well, I wouldn’t recommend this.

I use a full spectrum LED light most mornings for about 10-15 minutes while I catch up on email.  It’s great—if you try nothing else in here, this is the thing I’d try.  It’s a ridiculous gain for me.  I like this one, and it’s easy to travel with.

Exercise is probably the second most important physical factor.  I tried a number of different exercise programs for a few months each and the one that seemed best was lifting heavy weights 3x a week for an hour, and high intensity interval training occasionally.  In addition to productivity gains, this is also the exercise program that makes me feel the best overall.  

The third area is nutrition.  I very rarely eat breakfast, so I get about 15 hours of fasting most days (except an espresso when I wake up).  I know this is contrary to most advice, and I suspect it’s not optimal for most people, but it definitely works well for me.

Eating lots of sugar is the thing that makes me feel the worst and that I try hardest to avoid.  I also try to avoid foods that aggravate my digestion or spike up inflammation (for example, very spicy foods).  I don’t have much willpower when it comes to sweet things, so I mostly just try to keep junk food out of the house.

I have one big shot of espresso immediately when I wake up and one after lunch.  I assume this is about 200mg total of caffeine per day.  I tried a few other configurations; this was the one that worked by far the best.  I otherwise aggressively avoid stimulants, but I will have more coffee if I’m super tired and really need to get something done.

If a writer want to be super, then should include innovative thinking.

I’m vegetarian and have been since I was a kid, and I supplement methyl B-12, Omega-3, Iron, and Vitamin D-3.  I got to this list with a year or so of quarterly blood tests; it’s worked for me ever since (I re-test maybe every year and a half or so).  There are many doctors who will happily work with you on a super comprehensive blood test (and services like WellnessFX).  I also go out of my way to drink a lot of protein shakes, which I hate and I wouldn’t do if I weren’t vegetarian.

﻿Other stuff

Here’s what I like in a workspace: natural light, quiet, knowing that I won’t be interrupted if I don’t want to be, long blocks of time, and being comfortable and relaxed (I’ve got a beautiful desk with a couple of 4k monitors on it in my office, but I spend almost all my time on my couch with my laptop).

I wrote custom software for the annoying things I have to do frequently, which is great.  I also made an effort to learn to type really fast and the keyboard shortcuts that help with my workflow.

Like most people, I sometimes go through periods of a week or two where I just have no motivation to do anything (I suspect it may have something to do with nutrition).  This sucks and always seems to happen at inconvenient times.  I have not figured out what to do about it besides wait for the fog to lift, and to trust that eventually it always does.  And I generally try to avoid people and situations that put me in bad moods, which is good advice whether you care about productivity or not.

In general, I think it’s good to overcommit a little bit.  I find that I generally get done what I take on, and if I have a little bit too much to do it makes me more efficient at everything, which is a way to train to avoid distractions (a great habit to build!).  However, overcommitting a lot is disastrous.

Don’t neglect your family and friends for the sake of productivity—that’s a very stupid tradeoff (and very likely a net productivity loss, because you’ll be less happy).  Don’t neglect doing things you love or that clear your head either.

Finally, to repeat one more time: productivity in the wrong direction isn’t worth anything at all.  Think more about what to work on.

Open-Mindedness and curiosity are essential to writers

