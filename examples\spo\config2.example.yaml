llm:
  api_type: "openai"
  model: "gpt-4o-mini"
  base_url: "<your base url>"
  api_key: "<your api key>"
  temperature: 0
models:
 "gpt-4o": # model: "gpt-4-turbo"  # or gpt-3.5-turbo
   api_type: "openai"  # or azure / ollama / groq etc.
   base_url: "<your base url>"
   api_key: "<your api key>"
   temperature: 0
 "deepseek-chat": # api_type: "openai"  # or azure / ollama / groq etc.
   api_type: "openai"  # or azure / ollama / groq etc.
   base_url: "<your base url>"
   api_key: "<your api key>"
   temperature: 0
 "gpt-4o-mini": # api_type: "openai"  # or azure / ollama / groq etc.
   api_type: "openai"  # or azure / ollama / groq etc.
   base_url: "<your base url>"
   api_key: "<your api key>"
   temperature: 0

# Other models

