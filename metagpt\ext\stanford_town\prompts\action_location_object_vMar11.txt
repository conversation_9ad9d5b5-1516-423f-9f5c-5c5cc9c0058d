Variables: 
!<INPUT 0>! -- <PERSON>a name
!<INPUT 1>! -- <PERSON><PERSON>'s current arena
!<INPUT 2>! -- <PERSON><PERSON>'s current sector
!<INPUT 3>! -- <PERSON>a name
!<INPUT 4>! -- target sector
!<INPUT 5>! -- <PERSON><PERSON>'s sector's all arenas (minus no access)
!<INPUT 6>! -- Curr action seq
!<INPUT 7>! -- <PERSON>a name
!<INPUT 8>! -- <PERSON><PERSON>'s current sector

<commentblockmarker>###</commentblockmarker>
<PERSON> is in kitchen in <PERSON>'s house.
<PERSON> is going to <PERSON>'s house that has the following areas: {kitchen,  bedroom, bathroom}
Stay in the current area if the activity can be done there. Never go into other people's rooms unless necessary.
For cooking, <PERSON> should go to the following area in <PERSON>'s house:
Answer: {kitchen}
---
<PERSON> is in common room in <PERSON>'s apartment. 
<PERSON> is going to Hobbs Cafe that has the following areas: {cafe}
Stay in the current area if the activity can be done there. Never go into other people's rooms unless necessary.
For getting coffee, <PERSON> should go to the following area in Hobbs Cafe:
Answer: {cafe}
---

!<INPUT 0>! is going to !<INPUT 1>! that has the following areas: {!<INPUT 2>!}
* Stay in the current area if the activity can be done there. 
* NEVER go into other people's rooms unless necessary.
!<INPUT 3>! is !<INPUT 4>!. For !<INPUT 5>!, !<INPUT 6>! should go to the following area in !<INPUT 7>! (MUST pick one of {!<INPUT 8>!}):
Answer: {