generate_event_triple_v1.txt

Variables: 
!<INPUT 0>! -- <PERSON><PERSON>'s full name. 
!<INPUT 1>! -- Current action description
!<INPUT 2>! -- <PERSON><PERSON>'s full name. 

<commentblockmarker>###</commentblockmarker>
Task: Turn the input into (subject, predicate, object). 

Input: <PERSON> is eating breakfast. 
Output: (<PERSON>, eat, breakfast) 
--- 
Input: Joon Park is brewing coffee.
Output: (Joon Park, brew, coffee)
---
Input: <PERSON> is sleeping. 
Output: (<PERSON>, is, sleep)
---
Input: <PERSON> is writing email on a computer. 
Output: (<PERSON>, write, email)
---
Input: <PERSON> is teaching students in a classroom. 
Output: (<PERSON>, teach, students)
---
Input: <PERSON><PERSON> is running on a treadmill. 
Output: (<PERSON><PERSON>, run, treadmill)
---
Input: !<INPUT 0>! is !<INPUT 1>!. 
Output: (!<INPUT 2>!,
