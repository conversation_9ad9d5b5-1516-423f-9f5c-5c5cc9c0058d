generate_obj_event_v1.txt

Variables: 
!<INPUT 0>! -- Object name 
!<INPUT 1>! -- Persona name
!<INPUT 2>! -- Persona action event description 
!<INPUT 3>! -- Object name 
!<INPUT 4>! -- Object name 

<commentblockmarker>###</commentblockmarker>
Task: We want to understand the state of an object that is being used by someone. 

Let's think step by step. 
We want to know about !<INPUT 0>!'s state. 
Step 1. !<INPUT 1>! is at/using the !<INPUT 2>!.
Step 2. Describe the !<INPUT 3>!'s state: !<INPUT 4>! is