task_decomp_v2.txt

Variables: 
!<INPUT 0>! -- Commonset
!<INPUT 1>! -- Surrounding schedule description
!<INPUT 2>! -- <PERSON>a first name
!<INPUT 3>! -- <PERSON>a first name
!<INPUT 4>! -- Current action
!<INPUT 5>! -- curr time range
!<INPUT 6>! -- Current action duration in min
!<INPUT 7>! -- <PERSON>a first names

<commentblockmarker>###</commentblockmarker>
Describe subtasks in 5 min increments. 
---
Name: <PERSON>
Age: 35
Backstory: <PERSON> always wanted to be a teacher, and now she teaches kindergarten. During the week, she dedicates herself to her students, but on the weekends, she likes to try out new restaurants and hang out with friends. She is very warm and friendly, and loves caring for others.
Personality: sweet, gentle, meticulous
Location: <PERSON> is in an older condo that has the following areas: {kitchen, bedroom, dining, porch, office, bathroom, living room, hallway}.
Currently: <PERSON> is a teacher during the school year. She teaches at the school but works on lesson plans at home. She is currently living alone in a single bedroom condo.
Daily plan requirement: <PERSON> is planning to teach during the morning and work from home in the afternoon.s

Today is Saturday May 10. From 08:00am ~09:00am, <PERSON> is planning on having breakfast, from 09:00am ~ 12:00pm, <PERSON> is planning on working on the next day's kindergarten lesson plan, and from 12:00 ~ 13pm, <PERSON> is planning on taking a break. 
In 5 min increments, list the subtasks <PERSON> does when <PERSON> is working on the next day's kindergarten lesson plan from 09:00am ~ 12:00pm (total duration in minutes: 180):
1) <PERSON> is reviewing the kindergarten curriculum standards. (duration in minutes: 15, minutes left: 165)
2) <PERSON> is brainstorming ideas for the lesson. (duration in minutes: 30, minutes left: 135)
3) <PERSON> is creating the lesson plan. (duration in minutes: 30, minutes left: 105)
4) Kelly is creating materials for the lesson. (duration in minutes: 30, minutes left: 75)
5) Kelly is taking a break. (duration in minutes: 15, minutes left: 60)
6) Kelly is reviewing the lesson plan. (duration in minutes: 30, minutes left: 30)
7) Kelly is making final changes to the lesson plan. (duration in minutes: 15, minutes left: 15)
8) Kelly is printing the lesson plan. (duration in minutes: 10, minutes left: 5)
9) Kelly is putting the lesson plan in her bag. (duration in minutes: 5, minutes left: 0)
---
!<INPUT 0>!
!<INPUT 1>!
In 5 min increments, list the subtasks !<INPUT 2>! does when !<INPUT 3>! is !<INPUT 4>! from !<INPUT 5>! (total duration in minutes !<INPUT 6>!): 
1) !<INPUT 7>! is