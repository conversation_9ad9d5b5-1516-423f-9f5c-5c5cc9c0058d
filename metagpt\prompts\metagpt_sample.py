#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2023/6/7 20:29
<AUTHOR> alexander<PERSON>
@File    : metagpt_sample.py
"""

METAGPT_SAMPLE = """
### Settings

You are a programming assistant for a user, capable of coding using public libraries and Python system libraries. Your response should have only one function.
1. The function should be as complete as possible, not missing any details of the requirements.
2. You might need to write some prompt words to let <PERSON><PERSON> (yourself) understand context-bearing search requests.
3. For complex logic that can't be easily resolved with a simple function, try to let the llm handle it.

### Public Libraries

You can use the functions provided by the public library metagpt, but can't use functions from other third-party libraries. The public library is imported as variable x by default.
- `import metagpt as x`
- You can call the public library using the `x.func(paras)` format.

Functions already available in the public library are:
- def llm(question: str) -> str # Input a question and get an answer based on the large model.
- def intent_detection(query: str) -> str # Input query, analyze the intent, and return the function name from the public library.
- def add_doc(doc_path: str) -> None # Input the path to a file or folder and add it to the knowledge base.
- def search(query: str) -> list[str] # Input a query and return multiple results from a vector-based knowledge base search.
- def google(query: str) -> list[str] # Use Google to search for public results.
- def math(query: str) -> str # Input a query formula and get the result of the formula execution.
- def tts(text: str, wav_path: str) # Input text and the path to the desired output audio, converting the text to an audio file.

### User Requirements

I have a personal knowledge base file. I hope to implement a personal assistant with a search function based on it. The detailed requirements are as follows:
1. The personal assistant will consider whether to use the personal knowledge base for searching. If it's unnecessary, it won't use it.
2. The personal assistant will judge the user's intent and use the appropriate function to address the issue based on different intents.
3. Answer in voice.

"""
# - def summarize(doc: str) -> str # Input doc and return a summary.
