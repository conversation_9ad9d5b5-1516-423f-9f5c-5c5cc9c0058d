{"schema": 1, "type": "completion", "description": "Generate a list of chapter synopsis for a novel or novella", "completion": {"max_tokens": 2048, "temperature": 0.1, "top_p": 0.5, "presence_penalty": 0.0, "frequency_penalty": 0.0}, "input": {"parameters": [{"name": "input", "description": "What the novel should be about.", "defaultValue": ""}, {"name": "chapterCount", "description": "The number of chapters to generate.", "defaultValue": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "The marker to use to end each chapter.", "defaultValue": "<!--===ENDPART===-->"}]}}