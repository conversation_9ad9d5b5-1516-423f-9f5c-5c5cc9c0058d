#!/usr/bin/env python3
from __future__ import annotations

import sys


def print_flake8_output(input_string, show_line_numbers=False):
    for value in input_string.split("\n"):
        parts = value.split()
        if not show_line_numbers:
            print(f"- {' '.join(parts[1:])}")
        else:
            line_nums = ":".join(parts[0].split(":")[1:])
            print(f"- {line_nums} {' '.join(parts[1:])}")


if __name__ == "__main__":
    lint_output = sys.argv[1]
    print_flake8_output(lint_output)
