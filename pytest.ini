[pytest]
testpaths = tests
addopts = 
    --continue-on-collection-errors
    --doctest-modules
    --cov=./metagpt/
    --cov-report=xml:cov.xml
    --cov-report=html:htmlcov
    --durations=20
    --ignore=tests/metagpt/roles/di/run_swe_agent_for_benchmark.py
    --ignore=tests/metagpt/actions/di/test_write_analysis_code.py
    --ignore=tests/metagpt/actions/requirement_analysis/requirement/test_pic2txt.py
    --ignore=tests/metagpt/actions/test_action_multi_llm.py
    --ignore=tests/metagpt/actions/test_action_node.py
    --ignore=tests/metagpt/actions/test_debug_error.py
    --ignore=tests/metagpt/actions/test_design_api.py
    --ignore=tests/metagpt/actions/test_extract_readme.py
    --ignore=tests/metagpt/actions/test_import_repo.py
    --ignore=tests/metagpt/actions/test_invoice_ocr.py
    --ignore=tests/metagpt/actions/test_prepare_documents.py
    --ignore=tests/metagpt/actions/test_project_management.py
    --ignore=tests/metagpt/actions/test_rebuild_class_view.py
    --ignore=tests/metagpt/actions/test_research.py
    --ignore=tests/metagpt/actions/test_summarize_code.py
    --ignore=tests/metagpt/actions/test_write_code.py
    --ignore=tests/metagpt/actions/test_write_code_plan_and_change_an.py
    --ignore=tests/metagpt/actions/test_write_code_review.py
    --ignore=tests/metagpt/actions/test_write_prd.py
    --ignore=tests/metagpt/exp_pool/test_decorator.py
    --ignore=tests/metagpt/learn/test_text_to_image.py
    --ignore=tests/metagpt/learn/test_text_to_speech.py
    --ignore=tests/metagpt/management/test_skill_manager.py
    --ignore=tests/metagpt/memory/test_brain_memory.py
    --ignore=tests/metagpt/memory/test_longterm_memory.py
    --ignore=tests/metagpt/memory/test_memory.py
    --ignore=tests/metagpt/memory/test_memory_storage.py
    --ignore=tests/metagpt/memory/test_role_zero_memory.py
    --ignore=tests/metagpt/planner/test_action_planner.py
    --ignore=tests/metagpt/planner/test_basic_planner.py
    --ignore=tests/metagpt/provider/test_anthropic_api.py
    --ignore=tests/metagpt/provider/test_ark.py
    --ignore=tests/metagpt/provider/test_bedrock_api.py
    --ignore=tests/metagpt/provider/test_general_api_requestor.py
    --ignore=tests/metagpt/provider/test_openai.py
    --ignore=tests/metagpt/provider/test_qianfan_api.py
    --ignore=tests/metagpt/provider/zhipuai/test_async_sse_client.py
    --ignore=tests/metagpt/provider/zhipuai/test_zhipu_model_api.py
    --ignore=tests/metagpt/rag/engines/test_simple.py
    --ignore=tests/metagpt/rag/factories/test_base.py
    --ignore=tests/metagpt/rag/factories/test_embedding.py
    --ignore=tests/metagpt/rag/factories/test_index.py
    --ignore=tests/metagpt/rag/factories/test_llm.py
    --ignore=tests/metagpt/rag/factories/test_ranker.py
    --ignore=tests/metagpt/rag/factories/test_retriever.py
    --ignore=tests/metagpt/rag/parser/test_omniparse.py
    --ignore=tests/metagpt/rag/rankers/test_base_ranker.py
    --ignore=tests/metagpt/rag/rankers/test_object_ranker.py
    --ignore=tests/metagpt/rag/retrievers/test_base_retriever.py
    --ignore=tests/metagpt/rag/retrievers/test_bm25_retriever.py
    --ignore=tests/metagpt/rag/retrievers/test_chroma_retriever.py
    --ignore=tests/metagpt/rag/retrievers/test_es_retriever.py
    --ignore=tests/metagpt/rag/retrievers/test_faiss_retriever.py
    --ignore=tests/metagpt/rag/retrievers/test_hybrid_retriever.py
    --ignore=tests/metagpt/roles/di/test_data_analyst.py
    --ignore=tests/metagpt/roles/di/test_data_interpreter.py
    --ignore=tests/metagpt/roles/di/test_role_zero.py
    --ignore=tests/metagpt/roles/di/test_routing.py
    --ignore=tests/metagpt/roles/di/test_swe_agent.py
    --ignore=tests/metagpt/roles/di/test_team_leader.py
    --ignore=tests/metagpt/roles/test_architect.py
    --ignore=tests/metagpt/roles/test_assistant.py
    --ignore=tests/metagpt/roles/test_engineer.py
    --ignore=tests/metagpt/roles/test_invoice_ocr_assistant.py
    --ignore=tests/metagpt/roles/test_product_manager.py
    --ignore=tests/metagpt/roles/test_project_manager.py
    --ignore=tests/metagpt/roles/test_qa_engineer.py
    --ignore=tests/metagpt/roles/test_researcher.py
    --ignore=tests/metagpt/roles/test_role.py
    --ignore=tests/metagpt/roles/test_teacher.py
    --ignore=tests/metagpt/roles/test_tutorial_assistant.py
    --ignore=tests/metagpt/strategy/examples/test_creative_writing.py
    --ignore=tests/metagpt/strategy/examples/test_game24.py
    --ignore=tests/metagpt/strategy/test_planner.py
    --ignore=tests/metagpt/strategy/test_solver.py
    --ignore=tests/metagpt/test_config.py
    --ignore=tests/metagpt/test_context.py
    --ignore=tests/metagpt/test_context_mixin.py
    --ignore=tests/metagpt/test_document.py
    --ignore=tests/metagpt/test_environment.py
    --ignore=tests/metagpt/test_llm.py
    --ignore=tests/metagpt/test_message.py
    --ignore=tests/metagpt/test_prompt.py
    --ignore=tests/metagpt/test_repo_parser.py
    --ignore=tests/metagpt/test_reporter.py
    --ignore=tests/metagpt/test_role.py
    --ignore=tests/metagpt/test_schema.py
    --ignore=tests/metagpt/test_software_company.py
    --ignore=tests/metagpt/test_subscription.py
    --ignore=tests/metagpt/test_team.py
    --ignore=tests/metagpt/tools/libs/test_browser.py
    --ignore=tests/metagpt/tools/libs/test_cr.py
    --ignore=tests/metagpt/tools/libs/test_data_preprocess.py
    --ignore=tests/metagpt/tools/libs/test_editor.py
    --ignore=tests/metagpt/tools/libs/test_email_login.py
    --ignore=tests/metagpt/tools/libs/test_env.py
    --ignore=tests/metagpt/tools/libs/test_feature_engineering.py
    --ignore=tests/metagpt/tools/libs/test_gpt_v_generator.py
    --ignore=tests/metagpt/tools/libs/test_image_getter.py
    --ignore=tests/metagpt/tools/libs/test_index_repo.py
    --ignore=tests/metagpt/tools/libs/test_linter.py
    --ignore=tests/metagpt/tools/libs/test_sd_engine.py
    --ignore=tests/metagpt/tools/libs/test_shell.py
    --ignore=tests/metagpt/tools/libs/test_terminal.py
    --ignore=tests/metagpt/tools/libs/test_web_scraping.py
    --ignore=tests/metagpt/tools/test_azure_tts.py
    --ignore=tests/metagpt/tools/test_iflytek_tts.py
    --ignore=tests/metagpt/tools/test_metagpt_oas3_api_svc.py
    --ignore=tests/metagpt/tools/test_metagpt_text_to_image.py
    --ignore=tests/metagpt/tools/test_moderation.py
    --ignore=tests/metagpt/tools/test_openai_text_to_embedding.py
    --ignore=tests/metagpt/tools/test_openai_text_to_image.py
    --ignore=tests/metagpt/tools/test_openapi_v3_hello.py
    --ignore=tests/metagpt/tools/test_prompt_writer.py
    --ignore=tests/metagpt/tools/test_search_engine.py
    --ignore=tests/metagpt/tools/test_summarize.py
    --ignore=tests/metagpt/tools/test_tool_convert.py
    --ignore=tests/metagpt/tools/test_tool_recommend.py
    --ignore=tests/metagpt/tools/test_tool_registry.py
    --ignore=tests/metagpt/tools/test_translate.py
    --ignore=tests/metagpt/tools/test_ut_writer.py
    --ignore=tests/metagpt/tools/test_web_browser_engine.py
    --ignore=tests/metagpt/tools/test_web_browser_engine_playwright.py
    --ignore=tests/metagpt/tools/test_web_browser_engine_selenium.py
    --ignore=tests/metagpt/utils/test_ahttp_client.py
    --ignore=tests/metagpt/utils/test_code_parser.py
    --ignore=tests/metagpt/utils/test_common.py
    --ignore=tests/metagpt/utils/test_cost_manager.py
    --ignore=tests/metagpt/utils/test_custom_decoder.py
    --ignore=tests/metagpt/utils/test_dependency_file.py
    --ignore=tests/metagpt/utils/test_di_graph_repository.py
    --ignore=tests/metagpt/utils/test_file.py
    --ignore=tests/metagpt/utils/test_file_repository.py
    --ignore=tests/metagpt/utils/test_git_repository.py
    --ignore=tests/metagpt/utils/test_human_interaction.py
    --ignore=tests/metagpt/utils/test_json_to_markdown.py
    --ignore=tests/metagpt/utils/test_mermaid.py
    --ignore=tests/metagpt/utils/test_output_parser.py
    --ignore=tests/metagpt/utils/test_parse_html.py
    --ignore=tests/metagpt/utils/test_project_repo.py
    --ignore=tests/metagpt/utils/test_pycst.py
    --ignore=tests/metagpt/utils/test_redis.py
    --ignore=tests/metagpt/utils/test_repair_llm_raw_output.py
    --ignore=tests/metagpt/utils/test_repo_to_markdown.py
    --ignore=tests/metagpt/utils/test_s3.py
    --ignore=tests/metagpt/utils/test_save_code.py
    --ignore=tests/metagpt/utils/test_serialize.py
    --ignore=tests/metagpt/utils/test_session.py
    --ignore=tests/metagpt/utils/test_text.py
    --ignore=tests/metagpt/utils/test_token_counter.py
    --ignore=tests/metagpt/utils/test_tree.py
    --ignore=tests/metagpt/utils/test_visual_graph_repo.py
norecursedirs = 
    tests/metagpt/ext
    tests/metagpt/environment/android_env
    tests/metagpt/environment/werewolf_env
    tests/metagpt/environment/minecraft_env
    tests/metagpt/environment/stanford_town_env
    tests/metagpt/serialize_deserialize
