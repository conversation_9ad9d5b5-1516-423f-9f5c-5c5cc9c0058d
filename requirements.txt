aiohttp==3.8.6
#azure_storage==0.37.0
channels==4.0.0
# Django==4.1.5
# docx==0.2.4
#faiss==1.5.3
faiss_cpu==1.7.4
fire==0.4.0
typer==0.9.0
# godot==0.1.1
# google_api_python_client==2.93.0  # Used by search_engine.py
lancedb==0.4.0
loguru==0.6.0
meilisearch==0.21.0
numpy~=1.26.4
openai~=1.64.0
openpyxl~=3.1.5
beautifulsoup4==4.12.3
pandas==2.1.1
pydantic>=2.5.3
#pygame==2.1.3
# pymilvus==2.4.6
# pytest==7.2.2 # test extras require
python_docx==0.8.11
PyYAML==6.0.1
# sentence_transformers==2.2.2
setuptools>=65.6.3
tenacity==8.2.3
tiktoken==0.7.0
tqdm==4.66.2
#unstructured[local-inference]
# selenium>4
# webdriver_manager<3.9
anthropic==0.47.2
typing-inspect==0.8.0
libcst==1.0.1
qdrant-client==1.7.0
grpcio~=1.67.0
grpcio-tools~=1.62.3
grpcio-status~=1.62.3
# pytest-mock==3.11.1  # test extras require
# open-interpreter==0.1.7; python_version>"3.9" # Conflict with openai 1.x
ta==0.10.2
semantic-kernel==0.4.3.dev0
wrapt==1.15.0
#aiohttp_jinja2
# azure-cognitiveservices-speech~=1.31.0 # Used by metagpt/tools/azure_tts.py
#aioboto3~=12.4.0  # Used by metagpt/utils/s3.py
redis~=5.0.0 # Used by metagpt/utils/redis.py
curl-cffi~=0.7.0
httplib2~=0.22.0
websocket-client~=1.8.0
aiofiles==23.2.1
gitpython==3.1.40
zhipuai~=2.1.5
rich==13.6.0
nbclient==0.9.0
nbformat==5.9.2
ipython==8.17.2
ipykernel==6.27.1
scikit_learn==1.3.2
typing-extensions==4.11.0
socksio~=1.0.0
gitignore-parser==0.1.9
# connexion[uvicorn]~=3.0.5 # Used by metagpt/tools/openapi_v3_hello.py
websockets>=10.0,<12.0
networkx~=3.2.1
google-generativeai==0.4.1
playwright>=1.26  # used at metagpt/tools/libs/web_scraping.py
anytree
ipywidgets==8.1.1
Pillow
imap_tools==1.5.0  # Used by metagpt/tools/libs/email_login.py
pylint~=3.0.3
pygithub~=2.3
htmlmin
fsspec
grep-ast~=0.3.3  # linter
unidiff==0.7.5 # used at metagpt/tools/libs/cr.py
qianfan~=0.4.4
dashscope~=1.19.3
rank-bm25==0.2.2  # for tool recommendation
jieba==0.42.1  # for tool recommendation
volcengine-python-sdk[ark]~=1.0.94 # Solution for installation error in Windows: https://github.com/volcengine/volcengine-python-sdk/issues/5
gymnasium==0.29.1
boto3~=1.34.69
spark_ai_python~=0.3.30
tree_sitter~=0.23.2
tree_sitter_python~=0.23.2
httpx==0.28.1
