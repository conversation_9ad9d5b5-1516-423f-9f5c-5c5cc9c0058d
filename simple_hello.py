#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Simple Hello World script
"""

def main():
    print("Hello, world!")
    print("This is a simple Python script that doesn't rely on any external dependencies.")
    print("\nMetaGPT requires Python version <3.12,>=3.9, but your current Python version is 3.13.5.")
    print("To run MetaGPT, you would need to:")
    print("1. Install a compatible Python version (3.9, 3.10, or 3.11)")
    print("2. Create a virtual environment with that Python version")
    print("3. Install MetaGPT and its dependencies in that environment")
    print("4. Configure your OpenAI API key in the config file")

if __name__ == "__main__":
    main()